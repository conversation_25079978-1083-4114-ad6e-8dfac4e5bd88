
"use client";

import { useState, type FormEvent, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON><PERSON><PERSON>, Loader2, Al<PERSON><PERSON>riangle } from "lucide-react";
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { Progress } from '@/components/ui/progress';
import { type PlagiarismDetectionOutput } from '@/ai/flows/plagiarism-detection-flow';

interface PlagiarismDetectorProps {
  currentText: string;
  onResult: (result: PlagiarismDetectionOutput | null) => void;
}

export function PlagiarismDetector({ currentText, onResult }: PlagiarismDetectorProps) {
  const [analysisResult, setAnalysisResult] = useState<PlagiarismDetectionOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useI18n();

  useEffect(() => {
    onResult(analysisResult);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [analysisResult]);

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentText.trim()) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastEditorEmptyError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setAnalysisResult(null);

    try {
      const response = await fetch('/api/ai/detect-plagiarism', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentText }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result) {
        setAnalysisResult(result);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastPlagiarismDetectionSuccess" });
      } else {
        throw new Error("Plagiarism detection returned no result.");
      }
    } catch (error) {
      console.error("Error detecting plagiarism:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastPlagiarismDetectionError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-none shadow-none">
       <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('plagiarismDetectionDescription')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 p-0">
          {analysisResult && (
            <div className="space-y-3 p-3 bg-muted rounded-md">
              <div>
                <h4 className="font-semibold text-sm">{t('originalityScoreLabel')}:</h4>
                <div className="flex items-center gap-2 mt-1">
                    <Progress value={analysisResult.originalityScore} className="w-[calc(100%-4rem)] h-2.5" />
                    <span className="text-sm text-foreground font-medium">{analysisResult.originalityScore} / 100</span>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-sm mt-2">{t('plagiarismReportLabel')}:</h4>
                <p className="text-sm text-muted-foreground">{analysisResult.analysisReport}</p>
              </div>

              {analysisResult.detectedSources && analysisResult.detectedSources.length > 0 && (
                <div className="mt-4 space-y-3">
                  <h4 className="font-semibold text-base flex items-center text-destructive">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    {t('potentialSourcesFoundLabel')}
                  </h4>
                  <div className="space-y-2">
                    {analysisResult.detectedSources.map((source, index) => (
                      <div key={index} className="p-3 border-l-4 border-destructive bg-destructive/5 rounded-r-md">
                        <blockquote className="border-l-0 p-0">
                          <p className="text-sm font-medium text-destructive leading-relaxed">
                            "{source.plagiarizedSegment}"
                          </p>
                        </blockquote>
                        <div className="mt-2 text-xs text-muted-foreground">
                          <p>
                            <span className="font-semibold">{t('similarityScoreLabel')}:</span> {source.similarityScore}%
                          </p>
                          <p>
                            <span className="font-semibold">{t('originalSourceLabel')}:</span>{' '}
                            <a href={source.originalSource} target="_blank" rel="noopener noreferrer" className="underline hover:text-destructive transition-colors break-all">
                              {source.originalSource}
                            </a>
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
          {!isLoading && !analysisResult && currentText.trim().length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-8">{t('writeSomeTextToDetectPlagiarismPlaceholder')}</p>
          )}
        </CardContent>
        <CardFooter className="p-0 pt-4">
          <Button type="submit" disabled={isLoading || currentText.trim().length === 0} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <ShieldCheck className="mr-2 h-4 w-4" />}
            {t('detectPlagiarismButton')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
