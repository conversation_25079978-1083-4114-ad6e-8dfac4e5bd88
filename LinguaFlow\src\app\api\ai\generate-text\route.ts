import { NextRequest, NextResponse } from 'next/server';
import { generateText } from '@/ai/flows/ai-text-generation';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { prompt } = body;

    if (!prompt || typeof prompt !== 'string') {
      return NextResponse.json(
        { error: 'Prompt is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await generateText({ prompt });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in generate-text API:', error);
    return NextResponse.json(
      { error: 'Failed to generate text' },
      { status: 500 }
    );
  }
}
