import { z } from 'zod';
import { ai } from '@/ai/genkit';

// Enhanced suggestion schema with new categories
const EnhancedSuggestionSchema = z.object({
  id: z.string().describe('A unique identifier for the suggestion.'),
  category: z
    .enum(['spelling', 'grammar', 'style', 'vocabulary', 'plagiarism', 'similarity'])
    .describe(
      "The category of enhancement: 'spelling', 'grammar', 'style', 'vocabulary', 'plagiarism', or 'similarity'."
    ),
  type: z
    .enum(['spelling', 'grammar', 'rewrite', 'style'])
    .describe(
      "Legacy type field for compatibility."
    ),
  message: z.string().describe('A brief, user-friendly explanation of the enhancement and why it improves the text.'),
  suggestion: z.string().describe('The enhanced text segment with improved vocabulary, rhythm, or flow.'),
  originalSegment: z.string().describe('The original text segment that the suggestion refers to.'),
  startIndex: z
    .number()
    .optional()
    .describe(
      'The 0-based starting character index of the segment in the original text.'
    ),
  endIndex: z
    .number()
    .optional()
    .describe(
      'The 0-based ending character index (exclusive) of the segment in the original text.'
    ),
  confidence: z
    .number()
    .min(0)
    .max(1)
    .describe('Confidence score for the suggestion (0-1).'),
  severity: z
    .enum(['low', 'medium', 'high'])
    .describe('The severity/importance of the enhancement.'),
  enhancementType: z
    .enum(['vocabulary_upgrade', 'rhythm_improvement', 'flow_enhancement', 'engagement_boost'])
    .optional()
    .describe('Specific type of enhancement being suggested.'),
});

export type EnhancedSuggestion = z.infer<typeof EnhancedSuggestionSchema>;

const EnhancedTextAnalysisInputSchema = z.object({
  text: z.string().describe('The text to analyze for enhancements.'),
  language: z.string().describe('The ISO 639-1 language code of the text (e.g., "en", "es", "ar").'),
  focusAreas: z
    .array(z.enum(['vocabulary', 'rhythm', 'flow', 'engagement', 'clarity']))
    .optional()
    .describe('Specific areas to focus the analysis on.'),
  avoidRepetition: z
    .boolean()
    .optional()
    .default(true)
    .describe('Whether to avoid suggesting repetitive verbs or phrases.'),
});

export type EnhancedTextAnalysisInput = z.infer<typeof EnhancedTextAnalysisInputSchema>;

const EnhancedTextAnalysisOutputSchema = z.object({
  suggestions: z
    .array(EnhancedSuggestionSchema)
    .describe('A list of enhanced suggestions for the input text.'),
  overallScore: z
    .number()
    .min(0)
    .max(100)
    .describe('Overall writing quality score (0-100).'),
  rhythmScore: z
    .number()
    .min(0)
    .max(100)
    .describe('Rhythm and flow score (0-100).'),
  vocabularyScore: z
    .number()
    .min(0)
    .max(100)
    .describe('Vocabulary richness score (0-100).'),
  engagementScore: z
    .number()
    .min(0)
    .max(100)
    .describe('Reader engagement score (0-100).'),
});

export type EnhancedTextAnalysisOutput = z.infer<typeof EnhancedTextAnalysisOutputSchema>;

export async function analyzeTextEnhanced(input: EnhancedTextAnalysisInput): Promise<EnhancedTextAnalysisOutput | null> {
  return enhancedTextAnalysisFlow(input);
}

const enhancedTextAnalysisPrompt = ai.definePrompt({
  name: 'enhancedTextAnalysisPrompt',
  input: { schema: EnhancedTextAnalysisInputSchema },
  output: { schema: EnhancedTextAnalysisOutputSchema },
  prompt: `You are an expert writing coach specializing in creating lively, engaging, and rhythmically pleasing text. Your task is to analyze the provided text and suggest enhancements that focus on:

1. **Vocabulary Enhancement**: Replace mundane words with more vibrant, engaging alternatives
2. **Rhythm Improvement**: Enhance the natural flow and cadence of sentences
3. **Flow Enhancement**: Improve transitions and sentence structure for better readability
4. **Engagement Boost**: Make the text more captivating and dynamic

**Key Guidelines:**

- **Avoid Repetition**: Never suggest the same verb or phrase multiple times within the text
- **Focus on Rhythm**: Pay attention to sentence length variation, natural pauses, and reading flow
- **Vocabulary Upgrades**: Suggest more dynamic, specific, and engaging word choices
- **Preserve Meaning**: Maintain the original intent while enhancing expression
- **Contextual Awareness**: Consider the overall tone and style of the piece

**Enhancement Categories:**
- **vocabulary**: Replace weak or overused words with stronger alternatives
- **rhythm**: Improve sentence structure and pacing for better flow
- **flow**: Enhance transitions and logical progression
- **style**: General stylistic improvements for clarity and impact

**Scoring Criteria:**
- **Overall Score**: General writing quality (grammar, clarity, coherence)
- **Rhythm Score**: Natural flow, sentence variation, reading cadence
- **Vocabulary Score**: Word choice richness, specificity, engagement
- **Engagement Score**: How captivating and dynamic the text feels

**Important**: Only suggest enhancements when the text would genuinely benefit. Don't suggest changes to already smooth, well-flowing text unless there's a clear improvement opportunity.

Text to analyze: {{text}}
Language: {{language}}
Focus Areas: {{focusAreas}}
Avoid Repetition: {{avoidRepetition}}

Provide specific, actionable suggestions that will make the text more lively and engaging while maintaining its original meaning and intent.`,
});

const enhancedTextAnalysisFlow = ai.defineFlow(
  {
    name: 'enhancedTextAnalysisFlow',
    inputSchema: EnhancedTextAnalysisInputSchema,
    outputSchema: EnhancedTextAnalysisOutputSchema.nullable(),
  },
  async (input: EnhancedTextAnalysisInput): Promise<EnhancedTextAnalysisOutput | null> => {
    try {
      const { output } = await enhancedTextAnalysisPrompt(input);
      return output;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error(`[enhancedTextAnalysisFlow] - Error: ${errorMessage}`, { input });
      return null;
    }
  }
);
