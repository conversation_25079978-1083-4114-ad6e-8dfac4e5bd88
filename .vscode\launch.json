{"configurations": [{"type": "node", "request": "launch", "name": "Run autoprefixer", "runtimeExecutable": "autoprefixer", "cwd": "${workspaceFolder}/Tools", "args": []}, {"type": "java", "name": "<PERSON><PERSON><PERSON>", "request": "launch", "mainClass": "<PERSON><PERSON><PERSON>", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "PrimMST", "request": "launch", "mainClass": "Primsalgorithm.PrimMST", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "BinarySearchTree", "request": "launch", "mainClass": "BinarySearchTree", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "CS1102pakage", "request": "launch", "mainClass": "CS1102pakage", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "ReadRequest", "request": "launch", "mainClass": "webserver.ReadRequest", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "Runner", "request": "launch", "mainClass": "Runner", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "GeneralTreeTest", "request": "launch", "mainClass": "GeneralTreeTest", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "BtreeAlgorithm", "request": "launch", "mainClass": "BtreeAlgorithm", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "<PERSON><PERSON>", "request": "launch", "mainClass": "<PERSON><PERSON>", "projectName": "VsCode Projects_78933c54"}, {"type": "java", "name": "TestSuper", "request": "launch", "mainClass": "TestSuper", "projectName": "VsCode Projects_86d8b219"}, {"type": "java", "name": "BinarySearchTree", "request": "launch", "mainClass": "BinarySearchTree", "projectName": "VsCode Projects_86d8b219"}, {"type": "cmake", "request": "launch", "name": "Debug portfile(s)", "cmakeDebugType": "external", "pipeName": "\\\\.\\pipe\\vcpkg_ext_portfile_dbg", "preLaunchTask": "Debug vcpkg commands"}, {"name": "Containers: Node.js Launch", "type": "docker", "request": "launch", "preLaunchTask": "docker-run: debug", "platform": "node"}]}