{"version": 3, "sources": [], "sections": [{"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\nimport {googleAI} from '@genkit-ai/googleai';\n\nexport const ai = genkit({\n  plugins: [googleAI()],\n  model: 'googleai/gemini-2.0-flash',\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/word-toolkit-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to provide synonyms and spelling for a word.\n *\n * - getWordSuggestions - A function that handles the word analysis process.\n * - WordToolkitInput - The input type for the getWordSuggestions function.\n * - WordToolkitOutput - The return type for the getWordSuggestions function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst WordToolkitInputSchema = z.object({\n  word: z.string().describe('The single word to be analyzed.'),\n  context: z\n    .string()\n    .describe('The surrounding sentence or text to provide context.'),\n  language: z\n    .string()\n    .describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\").'),\n});\nexport type WordToolkitInput = z.infer<typeof WordToolkitInputSchema>;\n\nconst WordToolkitOutputSchema = z.object({\n  synonyms: z\n    .array(z.string())\n    .describe('An array of relevant synonyms for the word, based on its context. Should be 5 or less.'),\n  correctSpelling: z\n    .string()\n    .describe(\n      'The correct spelling of the word. If the word is already spelled correctly, returns the original word.'\n    ),\n});\nexport type WordToolkitOutput = z.infer<typeof WordToolkitOutputSchema>;\n\nexport async function getWordSuggestions(\n  input: WordToolkitInput\n): Promise<WordToolkitOutput> {\n  return wordToolkitFlow(input);\n}\n\nconst wordToolkitPrompt = ai.definePrompt({\n  name: 'wordToolkitPrompt',\n  input: {schema: WordToolkitInputSchema},\n  output: {schema: WordToolkitOutputSchema},\n  prompt: `You are a linguistic expert providing quick tools for writers. You will be given a specific word, its surrounding context, and its language.\n\nYour task is to provide a list of synonyms and the correct spelling for the given word in the specified language ({{language}}).\n\n- Synonyms should be relevant to the word's usage in the provided context. Provide up to 5 synonyms. If no relevant synonyms are found, return an empty array.\n- For spelling, if the word is already spelled correctly, return the word itself. If it is misspelled, return the correct spelling.\n\nAnalyze the following:\nWord: {{{word}}}\nContext: {{{context}}}\nLanguage: {{language}}\n`,\n});\n\nconst wordToolkitFlow = ai.defineFlow(\n  {\n    name: 'wordToolkitFlow',\n    inputSchema: WordToolkitInputSchema,\n    outputSchema: WordToolkitOutputSchema,\n  },\n  async (input: WordToolkitInput): Promise<WordToolkitOutput> => {\n    try {\n      const {output} = await wordToolkitPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : String(error);\n      console.error(`[wordToolkitFlow] - Error: ${errorMessage}`, {input});\n      // Return a default/empty state to prevent the client from crashing\n      // on transient errors like API quota limits.\n      return {\n        synonyms: [],\n        correctSpelling: input.word,\n      };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;;;;;;AAEA,MAAM,yBAAyB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,QAAQ,CAAC;IACZ,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,CAAC;AACd;AAGA,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,UAAU,oIAAA,CAAA,IAAC,CACR,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IACd,QAAQ,CAAC;IACZ,iBAAiB,oIAAA,CAAA,IAAC,CACf,MAAM,GACN,QAAQ,CACP;AAEN;AAGO,eAAe,uCAAgB,GAAhB,mBACpB,KAAuB;IAEvB,OAAO,gBAAgB;AACzB;AAEA,MAAM,oBAAoB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACxC,MAAM;IACN,OAAO;QAAC,QAAQ;IAAsB;IACtC,QAAQ;QAAC,QAAQ;IAAuB;IACxC,QAAQ,CAAC;;;;;;;;;;;AAWX,CAAC;AACD;AAEA,MAAM,kBAAkB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACnC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,kBAAkB;QACzC,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAClD,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,cAAc,EAAE;YAAC;QAAK;QAClE,mEAAmE;QACnE,6CAA6C;QAC7C,OAAO;YACL,UAAU,EAAE;YACZ,iBAAiB,MAAM,IAAI;QAC7B;IACF;AACF;;;IA/CoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-to-speech-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to convert a word into spoken audio of its spelling.\n *\n * - getSpelledOutAudio - A function that takes a word and generates a WAV audio data URI of it being spelled out.\n * - SpelledOutAudioInput - The input type for the getSpelledOutAudio function.\n * - SpelledOutAudioOutput - The return type for the getSpelledOutAudio function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {googleAI} from '@genkit-ai/googleai';\nimport {z} from 'genkit';\nimport wav from 'wav';\n\nconst SpelledOutAudioInputSchema = z.object({\n  word: z.string().describe('The word to be spelled out.'),\n  lang: z.string().describe('The BCP-47 language code for the pronunciation voice.'),\n});\nexport type SpelledOutAudioInput = z.infer<typeof SpelledOutAudioInputSchema>;\n\nconst SpelledOutAudioOutputSchema = z.object({\n  audioDataUri: z.string().nullable().describe(\"A data URI of the WAV audio file. Expected format: 'data:audio/wav;base64,<encoded_data>'. Is null on failure.\"),\n});\nexport type SpelledOutAudioOutput = z.infer<typeof SpelledOutAudioOutputSchema>;\n\nexport async function getSpelledOutAudio(input: SpelledOutAudioInput): Promise<SpelledOutAudioOutput> {\n  return spellWordToAudioFlow(input);\n}\n\nasync function toWav(\n  pcmData: Buffer,\n  channels = 1,\n  rate = 24000,\n  sampleWidth = 2\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const writer = new wav.Writer({\n      channels,\n      sampleRate: rate,\n      bitDepth: sampleWidth * 8,\n    });\n\n    const bufs: Buffer[] = [];\n    writer.on('error', reject);\n    writer.on('data', (d) => {\n      bufs.push(d);\n    });\n    writer.on('end', () => {\n      resolve(Buffer.concat(bufs).toString('base64'));\n    });\n\n    writer.write(pcmData);\n    writer.end();\n  });\n}\n\nconst spellWordToAudioFlow = ai.defineFlow(\n  {\n    name: 'spellWordToAudioFlow',\n    inputSchema: SpelledOutAudioInputSchema,\n    outputSchema: SpelledOutAudioOutputSchema,\n  },\n  async ({ word, lang }) => {\n    try {\n      // Format the word to be spelled out letter by letter.\n      const spelledOutWord = word.split('').join(' ');\n\n      const { media } = await ai.generate({\n        model: googleAI.model('gemini-2.5-flash-preview-tts'),\n        config: {\n          responseModalities: ['AUDIO'],\n          speechConfig: {\n            voiceConfig: {\n              prebuiltVoiceConfig: { voiceName: 'Algenib' }, // A standard voice\n            },\n          },\n        },\n        prompt: spelledOutWord,\n      });\n\n      if (!media) {\n        throw new Error('No audio media was generated by the model.');\n      }\n\n      const audioBuffer = Buffer.from(\n        media.url.substring(media.url.indexOf(',') + 1),\n        'base64'\n      );\n      \n      const wavBase64 = await toWav(audioBuffer);\n\n      return {\n        audioDataUri: 'data:audio/wav;base64,' + wavBase64,\n      };\n    } catch (error) {\n       const errorMessage = error instanceof Error ? error.message : String(error);\n       console.error(`[spellWordToAudioFlow] - Error: ${errorMessage}`, { word, lang });\n       // Return null instead of throwing an error to prevent server crashes.\n       return { audioDataUri: null };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;AAEA,MAAM,6BAA6B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,8BAA8B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC/C;AAGO,eAAe,uCAAgB,GAAhB,mBAAmB,KAA2B;IAClE,OAAO,qBAAqB;AAC9B;AAEA,eAAe,MACb,OAAe,EACf,WAAW,CAAC,EACZ,OAAO,KAAK,EACZ,cAAc,CAAC;IAEf,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI,4HAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YAC5B;YACA,YAAY;YACZ,UAAU,cAAc;QAC1B;QAEA,MAAM,OAAiB,EAAE;QACzB,OAAO,EAAE,CAAC,SAAS;QACnB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACjB,KAAK,IAAI,CAAC;QACZ;QACA,OAAO,EAAE,CAAC,OAAO;YACf,QAAQ,OAAO,MAAM,CAAC,MAAM,QAAQ,CAAC;QACvC;QAEA,OAAO,KAAK,CAAC;QACb,OAAO,GAAG;IACZ;AACF;AAEA,MAAM,uBAAuB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACnB,IAAI;QACF,sDAAsD;QACtD,MAAM,iBAAiB,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;QAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YAClC,OAAO,2KAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;YACtB,QAAQ;gBACN,oBAAoB;oBAAC;iBAAQ;gBAC7B,cAAc;oBACZ,aAAa;wBACX,qBAAqB;4BAAE,WAAW;wBAAU;oBAC9C;gBACF;YACF;YACA,QAAQ;QACV;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,OAAO,IAAI,CAC7B,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,IAC7C;QAGF,MAAM,YAAY,MAAM,MAAM;QAE9B,OAAO;YACL,cAAc,2BAA2B;QAC3C;IACF,EAAE,OAAO,OAAO;QACb,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,cAAc,EAAE;YAAE;YAAM;QAAK;QAC9E,sEAAsE;QACtE,OAAO;YAAE,cAAc;QAAK;IAC/B;AACF;;;IA3EoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 509, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file://g%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8BACA", "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}