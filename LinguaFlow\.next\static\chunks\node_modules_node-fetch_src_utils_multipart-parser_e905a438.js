(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_node-fetch_src_utils_multipart-parser_a98db7d7.js", {

"[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_node-fetch_src_utils_multipart-parser_f8cba6ac.js",
  "static/chunks/node_modules_node-fetch_src_utils_multipart-parser_47ddcd46.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/utils/multipart-parser.js [app-client] (ecmascript)");
    });
});
}}),
}]);