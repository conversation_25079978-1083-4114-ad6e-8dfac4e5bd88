"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useI18n } from '@/contexts/i18n-context';
import { 
  FileText, 
  Clock, 
  Target, 
  AlertTriangle, 
  CheckCircle,
  ShieldCheck,
  Sparkles,
  Edit3
} from 'lucide-react';

interface DocumentStatusBarProps {
  wordCount: number;
  charCount: number;
  readingTime: number;
  writingScore: number;
  spellingErrors: number;
  grammarErrors: number;
  styleIssues: number;
  vocabularyEnhancements: number;
  plagiarismIssues: number;
  similarityIssues: number;
  language: string;
  isAnalyzing: boolean;
}

export function DocumentStatusBar({
  wordCount,
  charCount,
  readingTime,
  writingScore,
  spellingErrors,
  grammarErrors,
  styleIssues,
  vocabularyEnhancements,
  plagiarismIssues,
  similarityIssues,
  language,
  isAnalyzing,
}: DocumentStatusBarProps) {
  const { t } = useI18n();

  const totalIssues = spellingErrors + grammarErrors + styleIssues + plagiarismIssues + similarityIssues;
  const totalEnhancements = vocabularyEnhancements;

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBadgeVariant = (score: number): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 80) return 'default';
    if (score >= 60) return 'secondary';
    return 'destructive';
  };

  return (
    <div className="flex items-center justify-between gap-4 p-3 border-t bg-muted/30 text-sm">
      {/* Left side - Document stats */}
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{wordCount}</span>
          <span className="text-muted-foreground">{t('wordsLabel')}</span>
        </div>

        <Separator orientation="vertical" className="h-4" />

        <div className="flex items-center gap-2">
          <span className="font-medium">{charCount}</span>
          <span className="text-muted-foreground">{t('charactersLabel')}</span>
        </div>

        <Separator orientation="vertical" className="h-4" />

        <div className="flex items-center gap-2">
          <Clock className="h-4 w-4 text-muted-foreground" />
          <span className="font-medium">{readingTime}</span>
          <span className="text-muted-foreground">{t('minReadLabel')}</span>
        </div>

        <Separator orientation="vertical" className="h-4" />

        <div className="flex items-center gap-2">
          <span className="text-muted-foreground">{t('languageLabel')}:</span>
          <Badge variant="outline" className="text-xs">
            {language.toUpperCase()}
          </Badge>
        </div>
      </div>

      {/* Center - Analysis status */}
      <div className="flex items-center gap-3">
        {isAnalyzing && (
          <div className="flex items-center gap-2 text-blue-600">
            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600"></div>
            <span className="text-xs">{t('analyzingLabel')}</span>
          </div>
        )}

        {/* Issue indicators */}
        {!isAnalyzing && (
          <div className="flex items-center gap-3">
            {spellingErrors > 0 && (
              <div className="flex items-center gap-1 text-red-600">
                <AlertTriangle className="h-3 w-3" />
                <span className="text-xs font-medium">{spellingErrors}</span>
                <span className="text-xs">{t('spellingLabel')}</span>
              </div>
            )}

            {grammarErrors > 0 && (
              <div className="flex items-center gap-1 text-red-600">
                <AlertTriangle className="h-3 w-3" />
                <span className="text-xs font-medium">{grammarErrors}</span>
                <span className="text-xs">{t('grammarLabel')}</span>
              </div>
            )}

            {styleIssues > 0 && (
              <div className="flex items-center gap-1 text-blue-600">
                <Edit3 className="h-3 w-3" />
                <span className="text-xs font-medium">{styleIssues}</span>
                <span className="text-xs">{t('styleLabel')}</span>
              </div>
            )}

            {vocabularyEnhancements > 0 && (
              <div className="flex items-center gap-1 text-green-600">
                <Sparkles className="h-3 w-3" />
                <span className="text-xs font-medium">{vocabularyEnhancements}</span>
                <span className="text-xs">{t('vocabularyLabel')}</span>
              </div>
            )}

            {plagiarismIssues > 0 && (
              <div className="flex items-center gap-1 text-red-700">
                <ShieldCheck className="h-3 w-3" />
                <span className="text-xs font-medium">{plagiarismIssues}</span>
                <span className="text-xs">{t('plagiarismLabel')}</span>
              </div>
            )}

            {similarityIssues > 0 && (
              <div className="flex items-center gap-1 text-yellow-600">
                <AlertTriangle className="h-3 w-3" />
                <span className="text-xs font-medium">{similarityIssues}</span>
                <span className="text-xs">{t('similarityLabel')}</span>
              </div>
            )}

            {totalIssues === 0 && totalEnhancements === 0 && !isAnalyzing && (
              <div className="flex items-center gap-1 text-green-600">
                <CheckCircle className="h-3 w-3" />
                <span className="text-xs">{t('noIssuesLabel')}</span>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Right side - Writing score */}
      <div className="flex items-center gap-2">
        <Target className="h-4 w-4 text-muted-foreground" />
        <span className="text-muted-foreground">{t('scoreLabel')}:</span>
        <Badge variant={getScoreBadgeVariant(writingScore)} className="font-medium">
          {writingScore}/100
        </Badge>
      </div>
    </div>
  );
}
