
"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Gauge, Loader2 } from "lucide-react";
// Removed direct import of server action
type AnalyzeToneOutput = {
  tone: string;
  confidence: number;
  reasoning: string;
};
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';

interface AiToneAnalyzerProps {
  currentText: string;
}

export function AiToneAnalyzer({ currentText }: AiToneAnalyzerProps) {
  const [analysisResult, setAnalysisResult] = useState<AnalyzeToneOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useI18n();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentText.trim()) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastEditorEmptyError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setAnalysisResult(null);

    try {
      const response = await fetch('/api/ai/analyze-tone', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentText }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result) {
        setAnalysisResult(result);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastToneAnalysisSuccess" });
      } else {
        throw new Error("Tone analysis returned no result.");
      }
    } catch (error) {
      console.error("Error analyzing tone:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastToneAnalysisError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('aiToneAnalysisDescription')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 p-0">
          {analysisResult && (
            <div className="space-y-3 p-3 bg-muted rounded-md">
              <div>
                <h4 className="font-semibold text-sm">{t('formalityLabel')}:</h4>
                <p className="text-sm text-muted-foreground">{analysisResult.formality}</p>
              </div>
              <div>
                <h4 className="font-semibold text-sm">{t('confidenceLabel')}:</h4>
                <p className="text-sm text-muted-foreground">{analysisResult.confidence}</p>
              </div>
              <div>
                <h4 className="font-semibold text-sm">{t('feedbackLabel')}:</h4>
                <p className="text-sm text-muted-foreground">{analysisResult.feedback}</p>
              </div>
            </div>
          )}
          {!isLoading && !analysisResult && currentText.trim().length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-8">{t('writeSomeTextToAnalyzePlaceholder')}</p>
          )}
           {!isLoading && !analysisResult && currentText.trim().length > 0 && (
             <p className="text-sm text-muted-foreground text-center py-8">{t('Click the button to analyze the tone.')}</p>
          )}
        </CardContent>
        <CardFooter className="p-0 pt-4">
          <Button type="submit" disabled={isLoading || currentText.length === 0} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Gauge className="mr-2 h-4 w-4" />}
            {t('analyzeToneButton')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
