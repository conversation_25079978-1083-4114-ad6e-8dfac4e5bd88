
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Loader2, CheckCircle, AlertTriangle } from "lucide-react";
// Removed type import - now defined locally
type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};
import { SuggestionItem } from './suggestion-item';
import { useI18n } from '@/contexts/i18n-context';
import { ScrollArea } from '@/components/ui/scroll-area';
import React from 'react';

interface WritingSuggestionsPanelProps {
  suggestions: AnalysisSuggestion[];
  isAnalyzing: boolean;
  onApplySuggestion: (
    suggestionText: string,
    originalSegment: string,
    startIndex?: number,
    endIndex?: number
  ) => void;
  onDismissSuggestion: (suggestionId: string) => void;
}

export function WritingSuggestionsPanel({ 
  suggestions, 
  isAnalyzing, 
  onApplySuggestion, 
  onDismissSuggestion
}: WritingSuggestionsPanelProps) {
  const { t } = useI18n();

  const hasSuggestions = suggestions.length > 0;
  
  const descriptionKey = isAnalyzing 
    ? 'analyzingTextDescription' 
    : hasSuggestions 
    ? 'suggestionsFoundDescription' 
    : 'startTypingForSuggestionsDescription';

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="p-3 border-b sticky top-0 bg-card z-10">
        <CardTitle className="text-base flex items-center">
            {isAnalyzing && <Loader2 className="h-3.5 w-3.5 animate-spin mr-2" />}
            {!isAnalyzing && hasSuggestions && <CheckCircle className="h-3.5 w-3.5 text-primary mr-2" />}
            {!isAnalyzing && !hasSuggestions && <AlertTriangle className="h-3.5 w-3.5 text-muted-foreground mr-2" />}
            {t('writingSuggestionsTitle')}
        </CardTitle>
        <CardDescription className="text-xs">
            {t(descriptionKey, { count: suggestions.length.toString() })}
        </CardDescription>
      </CardHeader>
      
      <CardContent className="flex-grow p-0 relative">
        <div className="absolute inset-0">
          <ScrollArea className="h-full w-full">
            <div className="p-1">
              {hasSuggestions ? (
                suggestions.map((suggestion) => (
                  <SuggestionItem
                    key={suggestion.id}
                    suggestion={suggestion}
                    onApply={onApplySuggestion}
                    onDismiss={onDismissSuggestion}
                  />
                ))
              ) : !isAnalyzing ? (
                <p className="p-4 text-sm text-muted-foreground text-center">{t('startTypingForSuggestionsDescription')}</p>
              ) : null}
            </div>
          </ScrollArea>
        </div>
      </CardContent>

      {hasSuggestions && (
        <CardFooter className="p-2 border-t text-xs text-muted-foreground justify-center sticky bottom-0 bg-card z-10">
          {t('suggestionsFoundDescription', { count: suggestions.length.toString() })}
        </CardFooter>
      )}
    </Card>
  );
}
