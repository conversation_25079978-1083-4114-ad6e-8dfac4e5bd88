import { NextRequest, NextResponse } from 'next/server';
import { humanizeText } from '@/ai/flows/humanize-ai-text-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await humanizeText({ text });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in humanize-text API:', error);
    return NextResponse.json(
      { error: 'Failed to humanize text' },
      { status: 500 }
    );
  }
}
