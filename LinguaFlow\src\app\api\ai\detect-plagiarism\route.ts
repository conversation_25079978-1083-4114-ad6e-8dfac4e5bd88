import { NextRequest, NextResponse } from 'next/server';
import { detectPlagiarism } from '@/ai/flows/plagiarism-detection-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await detectPlagiarism({ text });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in detect-plagiarism API:', error);
    return NextResponse.json(
      { error: 'Failed to detect plagiarism' },
      { status: 500 }
    );
  }
}
