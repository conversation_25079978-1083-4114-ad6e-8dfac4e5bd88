import { NextRequest, NextResponse } from 'next/server';
import { analyzeTone } from '@/ai/flows/ai-tone-analysis';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await analyzeTone({ text });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in analyze-tone API:', error);
    return NextResponse.json(
      { error: 'Failed to analyze tone' },
      { status: 500 }
    );
  }
}
