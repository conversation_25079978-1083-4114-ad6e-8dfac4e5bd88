{"appName": "LinguaFlow", "appDescription": "Grammar Correction & Writing Assistant", "editorTitle": "Editor", "rephraseSelectionButton": "Rephrase Selection", "writingStatsTitle": "Writing Statistics", "wordCountLabel": "Word Count", "charCountLabel": "Character Count", "writingScoreLabel": "Writing Score", "writingScoreUnit": "/ 100", "writingModeLabel": "Writing Mode", "selectWritingModePlaceholder": "Select writing mode", "formalWritingMode": "Formal", "casualWritingMode": "Casual", "professionalWritingMode": "Professional", "creativeWritingMode": "Creative", "technicalWritingMode": "Technical", "academicWritingMode": "Academic", "businessWritingMode": "Business", "aiToneAnalysisAccordionTitle": "Tone Analysis", "aiToneAnalysisTitle": "Tone Analysis", "aiToneAnalysisDescription": "Get feedback on your writing's formality and confidence.", "analyzeToneButton": "Analyze Text Tone", "formalityLabel": "Formality", "confidenceLabel": "Confidence", "feedbackLabel": "<PERSON><PERSON><PERSON>", "writeSomeTextToAnalyzePlaceholder": "Write some text in the editor to analyze its tone.", "aiTextGenerationAccordionTitle": "AI Content Generation", "aiTextGenerationTitle": "AI Content Generator", "aiTextGenerationDescription": "Generate content based on your prompt.", "yourPromptLabel": "Your Prompt", "promptPlaceholder": "e.g., Write a short story about a robot who discovers music", "generatedTextLabel": "Generated Text", "generateTextButton": "Generate Text", "settingsAccordionTitle": "Settings", "settingsTitle": "Settings", "settingsDescription": "Customize your LinguaFlow experience.", "themeLabel": "Theme", "switchToLightMode": "Switch to Light Mode", "switchToDarkMode": "Switch to Dark Mode", "languageLabel": "Language", "selectLanguagePlaceholder": "Select language", "englishUSLanguage": "English (US)", "englishUKLanguage": "English (UK)", "arabicLanguage": "العربية (Arabic)", "turkishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Turkish)", "spanishLanguage": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Spanish)", "germanLanguage": "<PERSON><PERSON><PERSON> (German)", "frenchLanguage": "<PERSON><PERSON><PERSON> (French)", "dutchLanguage": "<PERSON><PERSON><PERSON> (Dutch)", "italianLanguage": "<PERSON><PERSON> (Italian)", "startWritingPlaceholder": "Start writing your document here... The enhanced editor will provide real-time suggestions for spelling, grammar, style, and vocabulary improvements.", "rephrasePopoverTitle": "Rephrase Text", "rephrasePopoverDescription": "Review the suggestion for your selected text.", "originalTextLabel": "Original", "suggestionTextLabel": "Suggestion", "rephraseWaitMessage": "Click \"Rephrase\" or wait for suggestion.", "applyButton": "Apply", "cancelButton": "Cancel", "siteHeaderTitle": "LinguaFlow", "footerText": "Created by Eng: AZA7© 2025. All rights reserved®. Your support and recognition are greatly appreciated.", "toastInputRequiredTitle": "Input Required", "toastEditorEmptyError": "Editor is empty. Please write some text to analyze.", "toastPromptRequiredError": "Please enter a prompt to generate text.", "toastSuccessTitle": "Success", "toastErrorTitle": "Error", "toastInfoTitle": "Info", "toastTextGeneratedSuccess": "Text generated successfully.", "toastTextGenerationError": "Failed to generate text. Please try again.", "toastToneAnalysisSuccess": "Tone analysis complete.", "toastToneAnalysisError": "Failed to analyze tone. Please try again.", "toastNothingToRephraseError": "Nothing to rephrase", "toastSelectTextToRephraseError": "Please select some text in the editor.", "toastSuggestionReady": "Suggestion Ready", "toastRephraseError": "Failed to rephrase text. Please try again.", "toastFileUploadedSuccess": "File content loaded into editor.", "toastFileTypeNotSupportedError": "File type not supported. Please upload a {{fileType}} file.", "plagiarismDetectionAccordionTitle": "Plagiarism Detection", "plagiarismDetectionTitle": "Plagiarism Detection", "plagiarismDetectionSettingsTitle": "Plagiarism Detection", "plagiarismDetectionDescription": "Safeguard your integrity by utilizing our plagiarism detection tool, designed to meticulously scan your content for unintentional similarities with existing literature, helping you maintain originality in your writing.", "detectPlagiarismButton": "Detect Plagiarism Text", "originalityScoreLabel": "Originality Score", "plagiarismReportLabel": "Analysis Report", "potentialSourcesFoundLabel": "Potential Sources Found", "originalSourceLabel": "Original Source", "similarityScoreLabel": "Similarity Score", "toastPlagiarismDetectionSuccess": "Plagiarism check complete.", "toastPlagiarismDetectionError": "Failed to detect plagiarism. Please try again.", "writeSomeTextToDetectPlagiarismPlaceholder": "Write some text in the editor to check for plagiarism.", "aiWritingDetectionAccordionTitle": "AI Writing Detection", "aiWritingDetectionTitle": "AI Writing Detection", "aiWritingDetectionDescription": "Estimate the likelihood that your text was AI-generated.", "detectAiWritingButton": "Detect AI Writing Text", "probabilityAIWrittenLabel": "Probability AI Written", "aiWritingDetectionSummaryLabel": "Analysis Summary", "toastAiWritingDetectionSuccess": "AI writing detection complete.", "toastAiWritingDetectionError": "Failed to detect AI writing. Please try again.", "writeSomeTextToDetectAiWritingPlaceholder": "Write some text in the editor to check for AI authorship.", "writingSuggestionsTitle": "Writing Suggestions", "analyzingTextDescription": "AI is analyzing your text for suggestions...", "suggestionsFoundDescription": "{{count}} suggestions found. Review them below.", "noSuggestionsFoundDescription": "No immediate suggestions found. Keep writing or try rephrasing.", "startTypingForSuggestionsDescription": "Start typing for AI-powered writing suggestions.", "suggestionTypeSpelling": "Spelling", "suggestionTypeGrammar": "Grammar", "suggestionTypeRewrite": "Rewrite", "suggestionTypeStyle": "Style", "suggestionTypeUnknown": "Suggestion", "suggestionLabel": "Suggests", "applySuggestionButton": "Apply", "suggestionExplanationTooltip": "View explanation", "toastTextAnalysisError": "Failed to analyze text. Please try again.", "toastSuggestionAppliedSuccess": "Suggestion applied.", "toastSuggestionApplyError": "Could not apply suggestion. The original text might have changed.", "humanizeAiTextAccordionTitle": "Humanize AI Text", "humanizeAiTextTitle": "Humanize AI Text", "humanizeAiTextDescription": "Rewrite AI-generated text to sound more human-like.", "humanizeTextButton": "Humanize Text", "humanizedTextLabel": "Humanized Text", "toastHumanizeTextSuccess": "Text humanized successfully.", "toastHumanizeTextError": "Failed to humanize text. Please try again.", "writeSomeTextToHumanizePlaceholder": "Write or paste AI-generated text in the editor to humanize it.", "clearEditorButton": "Clear", "clearEditorButtonAriaLabel": "Clear all text from the editor", "toastEditorClearedSuccess": "Editor content cleared.", "generationHistoryTitle": "Generation History", "noGenerationsYetPlaceholder": "No generations yet. Generate some text to see it here.", "promptLabel": "Prompt", "outputLabel": "Output", "useThisPromptButton": "Use this Prompt", "copyOutputButton": "Copy Output", "toastPromptRestoredSuccess": "Prompt restored to input field.", "toastTextCopiedSuccess": "Text copied to clipboard.", "toastTextCopyError": "Failed to copy text to clipboard.", "insertIntoEditorButton": "Insert into Editor", "insertIntoEditorButtonTooltip": "Append generated text to the editor", "toastTextInsertedSuccess": "Generated text inserted into editor.", "copyEditorButton": "Copy Text", "copyEditorButtonAriaLabel": "Copy all text from the editor", "toastEditorContentCopiedSuccess": "Editor content copied to clipboard.", "toastEditorContentCopyError": "Failed to copy editor content to clipboard.", "toastEditorEmptyForCopyError": "Editor is empty. Nothing to copy.", "recordVoiceButtonStart": "Record Voice", "recordVoiceButtonStop": "Stop Recording", "recordVoiceButtonAriaLabelStart": "Start voice recording to transcribe to text", "recordVoiceButtonAriaLabelStop": "Stop voice recording", "toastRecordingStarted": "Recording started. Speak into your microphone.", "toastRecordingStoppedNoTranscript": "Recording stopped. No speech was transcribed.", "toastSpeechTranscribedAndAppended": "Speech transcribed and appended to editor.", "toastSpeechRecognitionNotSupported": "Speech recognition is not supported by your browser.", "toastMicrophonePermissionDenied": "Microphone permission denied. Please enable it in your browser settings and refresh the page.", "toastSpeechNoSpeechDetected": "No speech was detected. Please try again.", "toastSpeechAudioCaptureError": "Audio capture error. Please check your microphone.", "toastSpeechNetworkError": "Network error during speech recognition. Please check your connection.", "toastSpeechRecognitionError": "Speech recognition error: {{error}}", "toastSpeechServiceNotAllowed": "Speech recognition service is not allowed or unavailable. Please try again later.", "toastSpeechLanguageNotSupportedError": "The selected language is not supported for speech recognition by your browser.", "helpTitle": "Help", "helpPanelTitle": "How to Use LinguaFlow", "helpPanelDescription": "Get started with <PERSON>ua<PERSON><PERSON>'s features.", "helpPanelIntro": "Welcome to LinguaFlow! This guide will help you navigate and use the application's powerful writing assistance tools.", "helpEditorTitle": "The Editor", "helpEditorDescription": "The Editor is your main workspace. <br/><br/> <b>- Real-time Suggestions:</b> As you type, the app automatically checks your text and underlines potential issues. Click on a highlighted segment to see a correction popover. <br/> <b>- <PERSON> Tools on Selection:</b> Select any piece of text to bring up the <b>Smart Synonyms</b> button. If you select a single word, you'll get synonyms and a pronunciation guide. If you select a longer phrase, you'll get an AI-powered rephrasing suggestion. <br/> <b>- Formatting Toolbar:</b> At the top of the editor, you'll find tools to Undo/Redo, change fonts, and apply formatting like Bold, Italic, lists, and text alignment.", "helpAiToolsTitle": "AI Tools Panel", "helpAiToolsDescription": "The panels on the left and right provide powerful AI capabilities. <br/><br/> <b>- Write Tools (Left):</b> Here you can change the <b>Writing Mode</b> to influence the AI's style, <b>Import a Document</b>, or use the <b>AI Rewriter</b> to redraft your entire text. You can also use tools to <b>Humanize AI Text</b>, check for <b>AI Writing</b>, and detect <b>Plagiarism</b>. <br/> <b>- Analysis Tools (Right):</b> This column shows real-time <b>Writing Statistics</b>, provides a <b>Tone Analyzer</b> for your text, and includes the <b>AI Content Generator</b> to create new text from a prompt. Your generation history is saved here for easy reuse.", "helpLanguageSettingsTitle": "Language Settings", "helpLanguageSettingsDescription": "Configure the <b>UI Language</b> for the application's interface and the <b>Primary Writing Language</b> for analysis. For some languages, you can also select a <b>Regional Dialect</b>. Enable <b>Automatic Language Detection</b> to have the app switch writing languages as you type.", "helpAppearanceSettingsTitle": "Appearance Settings", "helpAppearanceSettingsDescription": "Customize the look and feel of the app. Choose a <b>Theme</b> (Light, Dark, or System), adjust the global <b>Font Size</b>, and toggle <b>High Contrast Mode</b> for better readability.", "helpDictionarySettingsTitle": "Dictionary Settings", "helpDictionarySettingsDescription": "Manage your personal dictionaries for different languages. <b>Add</b> words that you use often but might be flagged as misspellings (like names or technical jargon). You can also <b>Import</b> or <b>Export</b> your dictionary list as a JSON file, or <b>Clear</b> the dictionary for a specific language.", "helpFeatureSettingsTitle": "Feature Settings", "helpFeatureSettingsDescription": "Fine-tune the behavior of specific AI and auto-correction features. Here you can toggle on/off various generative AI tools, auto-correction behaviors, and the core real-time checking functionalities to match your workflow.", "helpWritingAidSettingsTitle": "Writing Aid Settings", "helpWritingAidSettingsDescription": "Customize how the AI assists you. Set your <b>Language Proficiency</b> to get suggestions tailored to your skill level. You can also enable or disable features like <b>Tone Detection</b>, <b>Plagiarism Detection</b>, and specialized support for <b>Non-native Speakers</b>.", "helpAdvancedSettingsTitle": "Advanced Settings", "helpAdvancedSettingsDescription": "Control core operational behaviors. Enable <b>Offline Functionality</b> to use basic features without an internet connection. If you ever want to start fresh, you can <b>Reset All Settings</b> to restore the application to its original defaults (this cannot be undone).", "helpPanelTip": "Experiment with different tools and settings to find what works best for your writing style and needs!", "Write Tools": "Write Tools", "Import Document": "Import Document", "Quick Action": "Quick Action", "Tone Analyzer": "<PERSON><PERSON>", "aiRewriteAccordionTitle": "AI Rewriter", "aiRewriteTitle": "AI Rewriter", "aiRewriteDescription": "Rewrite the entire editor content to enhance clarity and style.", "rewriteEditorContentButton": "Rewrite Editor Content", "rewrittenTextLabel": "Rewritten Text", "applyToEditorButton": "Apply to Editor", "toastRewriteSuccess": "Editor content rewritten successfully.", "toastRewriteError": "Failed to rewrite editor content. Please try again.", "writeSomeTextToRewritePlaceholder": "Write some text in the editor to rewrite it.", "Click the button to rewrite the editor content.": "Click the button to rewrite the editor content.", "dropzoneInstruction": "Drop files here or browse", "toastFileImportSuccessTitle": "File Imported", "toastFileImportSuccessMessage": "Document content has been loaded.", "toastFileImportErrorTitle": "Import Error", "toastFileImportErrorMessage": "Could not read the file content. Please ensure it's a valid .txt file.", "toastInvalidFileTypeMessage": "Invalid file type. Only .txt files are accepted.", "dropzoneAriaLabel": "Document import dropzone: Click or drag and drop a .txt file to upload.", "featuresLabel": "Features", "featureSettingsDescription": "Customize the functionality of specific writing assistance features.", "appearanceLabel": "Appearance", "writingAidLabel": "Writing Aid", "dictionaryLabel": "Dictionary", "dictionarySettingsDescription": "Add custom words or manage personal dictionaries. (Placeholder)", "advancedSettingsLabel": "Advanced", "advancedSettingsDescription": "Access advanced configuration options. Use with caution. (Placeholder)", "uiLanguageLabel": "UI Language", "selectUiLanguagePlaceholder": "Select UI language...", "uiLanguageDescription": "Changes the language of the application interface.", "writingLanguageLabel": "Primary Writing Language", "selectWritingLanguagePlaceholder": "Select writing language...", "writingLanguageDescription": "Sets the primary language for AI analysis and generation.", "regionalDialectLabel": "Regional Dialect", "selectRegionalDialectPlaceholder": "Select dialect...", "regionalDialectDescription": "Specifies the regional variation for the selected writing language.", "languageProficiencyLabel": "Language Proficiency", "selectProficiencyPlaceholder": "Select proficiency...", "languageProficiencyDescription": "Helps AI tailor suggestions to your language skill level.", "proficiencyNative": "Native", "proficiencyAdvanced": "Advanced (C1/C2)", "proficiencyIntermediate": "Intermediate (B1/B2)", "proficiencyBeginner": "<PERSON><PERSON><PERSON> (A1/A2)", "languageEnglishGeneral": "English", "languageSpanishGeneral": "Spanish", "languageFrenchGeneral": "French", "languageGermanGeneral": "German", "languageItalianGeneral": "Italian", "languageDutchGeneral": "Dutch", "languageArabicGeneral": "Arabic", "arabicSyriaLanguage": "Arabic (Syria)", "arabicSaudiArabiaLanguage": "Arabic (Saudi Arabia)", "arabicEgyptLanguage": "Arabic (Egypt)", "languageTurkishGeneral": "Turkish", "spanishSpainLanguage": "Spanish (Spain)", "spanishMexicoLanguage": "Spanish (Mexico)", "themeLight": "Light", "themeDark": "Dark", "themeSystem": "System Default", "selectThemePlaceholder": "Select theme...", "themeDescription": "Choose the application's visual theme.", "fontSizeLabel": "Font Size", "selectFontSizePlaceholder": "Select font size...", "fontSizeSmall": "Small", "fontSizeMedium": "Medium", "fontSizeLarge": "Large", "fontSizeDescription": "Adjust the text size throughout the application.", "highContrastModeLabel": "High Contrast Mode", "highContrastModeDescription": "Increases text/background contrast for better readability.", "enabledLabel": "Enabled", "disabledLabel": "Disabled", "personalDictionaryLabel": "Personal Dictionary", "personalDictionaryDescription": "Add words you use frequently that might be flagged as errors.", "addWordPlaceholder": "Enter a word...", "addWordButton": "Add Word", "deleteWordButtonAria": "Delete word {{word}}", "dictionaryEmptyPlaceholder": "Your dictionary is empty. Add some words!", "dictionaryImportExportLabel": "Import / Export Dictionary", "importDictionaryButton": "Import", "exportDictionaryButton": "Export", "dictionaryImportExportDescription": "Backup or share your personal dictionary as a JSON file.", "clearDictionaryForLanguageButton": "Clear {{language}} Dictionary", "clearDictionaryConfirmTitle": "Are you sure?", "clearDictionaryForLanguageConfirmDescription": "This will permanently delete all words from your personal dictionary for {{language}}. This action cannot be undone.", "confirmResetButton": "Yes, Reset Everything", "clearDictionaryWarning": "This action is irreversible.", "toastDictionaryWordAdded": "Word '{{word}}' added to dictionary.", "toastDictionaryWordExists": "Word '{{word}}' already exists in dictionary.", "toastDictionaryWordEmpty": "Cannot add an empty word.", "toastDictionaryWordDeleted": "Word '{{word}}' deleted from dictionary.", "toastDictionaryImportOverwriteSuccess": "{{count}} words imported, dictionary overwritten.", "toastDictionaryImportMergeSuccess": "{{count}} new words imported and merged.", "toastDictionaryImportInvalidFormat": "Invalid dictionary file format. Must be a JSON array of strings.", "toastDictionaryImportError": "Error importing dictionary file.", "toastDictionaryExportSuccess": "Dictionary exported successfully.", "toastDictionaryCleared": "Personal dictionary cleared.", "generativeAiFeaturesLabel": "Generative AI Features", "showAiSuggestionsOnTextSelectionLabel": "Show AI Suggestions on Text Selection", "showAiSuggestionsOnTextSelectionDescription": "Enable AI suggestions when you select text.", "quickAiActionsForSelectedTextLabel": "Quick AI Actions for Selected Text", "quickAiActionsForSelectedTextDescription": "Access quick AI actions for highlighted text.", "aiQuickReplySuggestionsLabel": "AI-Based Quick Reply Suggestions", "aiQuickReplySuggestionsDescription": "Receive ideas for replies. (Mocked feature)", "viewAndReuseRecentPromptHistoryLabel": "View and Reuse Recent Prompt History", "viewAndReuseRecentPromptHistoryDescription": "Easily access your recent AI prompts.", "autoCorrectionFeaturesLabel": "Auto-Correction Features", "automaticTextCorrectionLabel": "Automatic Text Correction", "automaticTextCorrectionDescription": "Automatically fix common typos and grammatical errors.", "realTimeCorrectionLabel": "Real-Time Correction", "realTimeCorrectionDescription": "Apply corrections as you type.", "sentenceEnhancementLabel": "Sentence Enhancement", "sentenceEnhancementDescription": "Get suggestions for improving clarity and style.", "showCorrectionFeedbackLabel": "Show Correction Feedback", "showCorrectionFeedbackDescription": "Receive notifications when corrections are applied.", "coreCheckingFeaturesLabel": "Core Checking Features", "realTimeGrammarCheckingLabel": "Real-Time Grammar Checking", "realTimeGrammarCheckingDescription": "Get instant feedback on grammar as you write.", "realTimeSpellCheckingLabel": "Real-Time Spell Checking", "realTimeSpellCheckingDescription": "Check for spelling mistakes in real time.", "styleSuggestionsLabel": "Style Suggestions", "styleSuggestionsDescription": "Get suggestions to improve your writing style.", "toastLanguageSwitched": "Writing language automatically switched to {{language}}.", "writingAssistanceTitle": "Writing Assistance", "writingAssistanceDescription": "Enhance your writing experience with LinguaFlow by customizing how it assists you.", "yourLanguageProficiencyTitle": "Your Language Proficiency (for Primary Language)", "yourLanguageProficiencyDescription": "Receive meticulously tailored suggestions that align with your sophisticated understanding of language, ensuring a more nuanced approach to your writing.", "toneDetectionTitle": "Tone Detection", "toneDetectionDescription": "Delve into the emotional undertones of your text with our tone detection feature, which analyzes your writing and offers insightful suggestions to refine and elevate your tone, making it resonate more effectively with your audience.", "nonNativeSupportTitle": "Support for Non-native Speakers", "nonNativeSupportDescription": "Benefit from specialized assistance aimed at non-native speakers, providing thoughtful guidance and practical tips to enhance your fluency and confidence in writing.", "advancedSettingsTitle": "Advanced Settings", "enableOfflineFunctionalityLabel": "Enable Offline Functionality", "enableOfflineFunctionalityDescription": "Basic features, including settings and the dictionary, are available offline. Key components will be included to ensure seamless program function.", "enableAutomaticLanguageDetectionLabel": "Enable Automatic Language Detection", "enableAutomaticLanguageDetectionDescription": "Automatically detect and switch the writing language as you type.", "dataManagementLabel": "Data Management", "resetAllSettingsLabel": "Reset All Settings", "resetAllSettingsDescription": "This will reset all customizations including theme, language, and feature settings to their defaults. This action cannot be undone.", "resetButtonLabel": "Reset", "resetAllSettingsConfirmTitle": "Are you sure you want to reset all settings?", "resetAllSettingsConfirmDescription": "All your personal settings, dictionary words, and preferences will be permanently deleted and reset to the application defaults. This action cannot be undone.", "toastResetSuccess": "All settings have been reset to default. The application will now reload.", "dictionaryLanguageLabel": "Dictionary Language", "selectDictionaryLanguagePlaceholder": "Select language to view...", "dictionaryLanguageDescription": "View and manage the dictionary for a specific language.", "toastSuggestionDismissed": "Suggestion dismissed.", "dismissButton": "<PERSON><PERSON><PERSON>", "correctButton": "Correct", "undoButton": "Undo", "redoButton": "Redo", "aiToolsButton": "Smart Synonyms", "wordToolkitTitle": "Synonyms Suggestion", "wordToolkitDescription": "Select a single word in the editor to get synonyms and hear its pronunciation.", "wordToolkitPlaceholder": "Select a single word in the main editor to get smart synonyms.", "selectedWordLabel": "Selected Word", "synonymsLabel": "Synonyms", "noSynonymsFound": "No synonyms found.", "applySynonymTooltip": "Replace word with '{{synonym}}'", "toastWordToolkitError": "Failed to get suggestions for the selected word.", "toastWordReplacedSuccess": "Word replaced with '{{word}}'.", "wordToolkitPopoverDescription": "Get synonyms or hear the word's pronunciation.", "pronunciationLabel": "Pronunciation", "pronounceButton": "Pronounce Word", "toastPronunciationError": "Failed to generate audio pronunciation.", "toastEmptyText": "Please enter some text first.", "saveButton": "Save", "exportButton": "Export", "wordsLabel": "words", "charactersLabel": "characters", "minReadLabel": "min read", "analyzingLabel": "Analyzing", "spellingLabel": "spelling", "grammarLabel": "grammar", "styleLabel": "style", "vocabularyLabel": "vocabulary", "plagiarismLabel": "plagiarism", "similarityLabel": "similarity", "noIssuesLabel": "No issues found", "scoreLabel": "Score", "toastAiRewriteError": "Failed to rewrite text. Please try again.", "toastAiGenerateError": "Failed to generate text. Please try again."}