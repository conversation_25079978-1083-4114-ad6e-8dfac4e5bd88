
"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { User<PERSON>heck, Loader2, ClipboardPaste } from "lucide-react";
// Removed direct import of server action
type HumanizeTextOutput = {
  humanizedText: string;
};
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';

interface HumanizeAiTextProps {
  currentText: string;
  onInsertText: (text: string) => void;
}

export function HumanizeAiText({ currentText, onInsertText }: HumanizeAiTextProps) {
  const [humanizedOutput, setHumanizedOutput] = useState<HumanizeTextOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useI18n();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentText.trim()) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastEditorEmptyError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setHumanizedOutput(null);

    try {
      const response = await fetch('/api/ai/humanize-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentText }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result) {
        setHumanizedOutput(result);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastHumanizeTextSuccess" });
      } else {
        throw new Error("Humanize text returned no result.");
      }
    } catch (error) {
      console.error("Error humanizing text:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastHumanizeTextError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInsertIntoEditor = () => {
    if (humanizedOutput?.humanizedText) {
      onInsertText(humanizedOutput.humanizedText);
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextInsertedSuccess" });
    }
  };

  return (
    <Card className="border-none shadow-none">
       <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('humanizeAiTextDescription')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 p-0">
          {humanizedOutput && (
            <div>
              <Label htmlFor="humanized-text-output" className="text-xs text-muted-foreground">{t('humanizedTextLabel')}</Label>
              <Textarea
                id="humanized-text-output"
                value={humanizedOutput.humanizedText}
                readOnly
                className="mt-1 min-h-[120px] bg-muted font-code"
              />
              <div className="mt-2 flex justify-end">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleInsertIntoEditor}
                  disabled={isLoading || !humanizedOutput?.humanizedText}
                  title={t('insertIntoEditorButtonTooltip')}
                >
                  <ClipboardPaste className="mr-2 h-3.5 w-3.5" />
                  {t('insertIntoEditorButton')}
                </Button>
              </div>
            </div>
          )}
          {!isLoading && !humanizedOutput && currentText.trim().length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-8">{t('writeSomeTextToHumanizePlaceholder')}</p>
          )}
        </CardContent>
        <CardFooter className="p-0 pt-4">
          <Button type="submit" disabled={isLoading || currentText.trim().length === 0} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <UserCheck className="mr-2 h-4 w-4" />}
            {t('humanizeTextButton')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
