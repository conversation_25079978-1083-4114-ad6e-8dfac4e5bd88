
"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Loader2, CheckSquare, Wand2 } from "lucide-react";
// Removed direct import of server action
type RephraseTextInput = {
  text: string;
  writingMode: string;
  language: string;
};
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';

interface AiRewriterProps {
  currentText: string;
  writingMode: string;
  onApplyRewrite: (text: string) => void;
  direction: 'ltr' | 'rtl';
}

export function AiRewriter({ currentText, writingMode, onApplyRewrite, direction }: AiRewriterProps) {
  const [rewrittenOutput, setRewrittenOutput] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useI18n();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentText.trim()) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastEditorEmptyError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setRewrittenOutput(null);

    try {
      const response = await fetch('/api/ai/rephrase-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedText: currentText,
          contextText: currentText,
          tone: writingMode,
          style: writingMode,
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result && result.rephrasedText) {
        setRewrittenOutput(result.rephrasedText);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastRewriteSuccess" });
      } else {
        throw new Error("AI did not return a valid rewrite.");
      }
    } catch (error) {
      console.error("Error rewriting text:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastRewriteError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyToEditor = () => {
    if (rewrittenOutput) {
      onApplyRewrite(rewrittenOutput);
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextInsertedSuccess" }); 
    }
  };

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('aiRewriteDescription')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 p-0">
          {rewrittenOutput && (
            <div className="relative">
              <Label htmlFor="rewritten-text-output" className="text-xs text-muted-foreground">{t('rewrittenTextLabel')}</Label>
              <Textarea
                id="rewritten-text-output"
                value={rewrittenOutput}
                readOnly
                dir={direction}
                className="mt-1 min-h-[120px] bg-muted font-code pr-24"
              />
              <div className="absolute bottom-2 right-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleApplyToEditor}
                  disabled={isLoading || !rewrittenOutput}
                  title={t('applyToEditorButton')}
                >
                  <CheckSquare className="mr-2 h-3.5 w-3.5" />
                  {t('applyToEditorButton')}
                </Button>
              </div>
            </div>
          )}
          {!isLoading && !rewrittenOutput && currentText.trim().length === 0 && (
            <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">{t('writeSomeTextToRewritePlaceholder')}</p>
            </div>
          )}
           {!isLoading && !rewrittenOutput && currentText.trim().length > 0 && (
             <div className="text-center py-8">
              <p className="text-sm text-muted-foreground">{t('Click the button to rewrite the editor content.')}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="p-0 pt-4">
          <Button type="submit" disabled={isLoading || currentText.trim().length === 0} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Wand2 className="mr-2 h-4 w-4" />}
            {t('rewriteEditorContentButton')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
