"use client";

import React, { useState, useEffect, useCallback, useRef, useMemo, forwardRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { useDebounce } from '@/hooks/use-debounce';
import { cn } from '@/lib/utils';
import { Check, X, AlertTriangle, ShieldCheck, Sparkles, Edit3 } from 'lucide-react';

// Removed type import - now defined locally
type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};

// Legacy compatibility types
export interface PlagiarismSource {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  url?: string;
  title?: string;
  similarity?: number;
  matchedText?: string;
}

export interface SimilaritySource {
  url: string;
  title: string;
  similarity: number;
  matchedText: string;
}

// Enhanced suggestion types with new categories
export interface EnhancedSuggestion extends AnalysisSuggestion {
  category: 'spelling' | 'grammar' | 'style' | 'vocabulary' | 'plagiarism' | 'similarity';
  confidence: number;
  replacements?: string[];
  explanation?: string;
  source?: string;
  context?: {
    before: string;
    after: string;
  };
}

// Enhanced document analysis interface
export interface DocumentAnalysis {
  suggestions: EnhancedSuggestion[];
  metrics: {
    readabilityScore: number;
    wordCount: number;
    sentenceCount: number;
    avgWordsPerSentence: number;
    complexWords: number;
    passiveVoice: number;
  };
  plagiarismCheck?: {
    similarity: number;
    sources: Array<{
      url: string;
      title: string;
      similarity: number;
      matchedText: string;
    }>;
  };
}

// Enhanced props interface
export interface EnhancedDocumentEditorProps {
  value: string;
  onChange: (value: string) => void;
  onAnalysisComplete?: (analysis: DocumentAnalysis) => void;
  placeholder?: string;
  disabled?: boolean;
  autoAnalyze?: boolean;
  analysisDelay?: number;
  showMetrics?: boolean;
  enablePlagiarismCheck?: boolean;
  enableGrammarCheck?: boolean;
  enableStyleSuggestions?: boolean;
  enableVocabularyEnhancement?: boolean;
  maxSuggestions?: number;
  language?: string;
  suggestions?: EnhancedSuggestion[];
  onApplySuggestion?: (suggestion: EnhancedSuggestion) => void;
  onDismissSuggestion?: (suggestionId: string) => void;
  isAnalyzing?: boolean;
  analysisProgress?: number;
  similaritySources?: Array<{
    url: string;
    title: string;
    similarity: number;
    matchedText: string;
  }>;
  plagiarismSources?: PlagiarismSource[];
  writingMode?: 'formal' | 'casual' | 'academic' | 'creative';
  direction?: 'ltr' | 'rtl';
  onUndo?: () => void;
  onRedo?: () => void;
  canUndo?: boolean;
  canRedo?: boolean;
  className?: string;
}

// Enhanced Document Editor Component
const EnhancedDocumentEditor = forwardRef<HTMLTextAreaElement, EnhancedDocumentEditorProps>(({
    value,
    onChange,
    onAnalysisComplete,
    placeholder = "Start writing your document...",
    disabled = false,
    autoAnalyze = true,
    analysisDelay = 1000,
    showMetrics = true,
    enablePlagiarismCheck = false,
    enableGrammarCheck = true,
    enableStyleSuggestions = true,
    enableVocabularyEnhancement = true,
    maxSuggestions = 10,
    language = 'en',
    suggestions = [],
    isAnalyzing = false,
    analysisProgress = 0,
    similaritySources = [],
    plagiarismSources = [],
    onApplySuggestion,
    onDismissSuggestion,
    writingMode,
    direction,
    onUndo,
    onRedo,
    canUndo,
    canRedo,
    className,
  }, ref) => {
    const { t } = useI18n();
    const { toast } = useToast();
    const textareaRef = useRef<HTMLTextAreaElement>(null);
    const [selectedSuggestion, setSelectedSuggestion] = useState<EnhancedSuggestion | null>(null);
    const [popoverOpen, setPopoverOpen] = useState(false);
    const [cursorPosition, setCursorPosition] = useState(0);
    const [localSuggestions, setLocalSuggestions] = useState<EnhancedSuggestion[]>(suggestions);
    
    // Debounced value for analysis
    const debouncedValue = useDebounce(value, analysisDelay);
    
    // Mock analysis function - in real implementation, this would call your AI service
    const analyzeDocument = useCallback(async (text: string): Promise<DocumentAnalysis> => {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock analysis results
      const mockSuggestions: EnhancedSuggestion[] = [
        {
          id: '1',
          type: 'grammar',
          category: 'grammar',
          message: 'Consider using active voice',
          suggestion: 'The team completed the project',
          originalSegment: 'The project was completed by the team',
          severity: 'medium',
          confidence: 0.85,
          startIndex: 0,
          endIndex: 35,
          explanation: 'Active voice makes your writing more direct and engaging.'
        }
      ];
      
      return {
        suggestions: mockSuggestions,
        metrics: {
          readabilityScore: 75,
          wordCount: text.split(' ').length,
          sentenceCount: text.split('.').length - 1,
          avgWordsPerSentence: 15,
          complexWords: 5,
          passiveVoice: 2
        },
        plagiarismCheck: enablePlagiarismCheck ? {
          similarity: 15,
          sources: similaritySources
        } : undefined
      };
    }, [enablePlagiarismCheck, similaritySources]);
    
    // Auto-analyze when content changes
    useEffect(() => {
      if (autoAnalyze && debouncedValue.trim()) {
        analyzeDocument(debouncedValue)
          .then(analysis => {
            setLocalSuggestions(analysis.suggestions.slice(0, maxSuggestions));
            onAnalysisComplete?.(analysis);
          })
          .catch(error => {
            console.error('Analysis failed:', error);
          });
      }
    }, [debouncedValue, autoAnalyze, analyzeDocument, maxSuggestions, onAnalysisComplete]);
    
    return (
      <div className={cn("relative", className)}>
        <Textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          className="min-h-[200px] resize-none"
          dir={direction}
        />
      </div>
    );
});

EnhancedDocumentEditor.displayName = 'EnhancedDocumentEditor';

export default EnhancedDocumentEditor;
