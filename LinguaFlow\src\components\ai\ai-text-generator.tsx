
"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Sparkles, Loader2, Wand2, History, Copy, ClipboardEdit, ClipboardPaste } from "lucide-react";
// Removed direct import of server action
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';

interface GenerationHistoryItem {
  id: string;
  prompt: string;
  generatedText: string;
}

interface AiTextGeneratorProps {
  onInsertText: (text: string) => void;
}

const MAX_HISTORY_ITEMS = 10;

export function AiTextGenerator({ onInsertText }: AiTextGeneratorProps) {
  const [prompt, setPrompt] = useState("");
  const [generatedOutput, setGeneratedOutput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [generationHistory, setGenerationHistory] = useState<GenerationHistoryItem[]>([]);
  const { toast } = useToast();
  const { t } = useI18n();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const currentPrompt = prompt.trim();
    if (!currentPrompt) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastPromptRequiredError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setGeneratedOutput("");

    try {
      const response = await fetch('/api/ai/generate-text', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: currentPrompt }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result && result.generatedText) {
        setGeneratedOutput(result.generatedText);
        setGenerationHistory(prev => 
          [{ id: Date.now().toString(), prompt: currentPrompt, generatedText: result.generatedText }, ...prev].slice(0, MAX_HISTORY_ITEMS)
        );
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextGeneratedSuccess" });
      } else {
        throw new Error("AI did not return valid generated text.");
      }
    } catch (error) {
      console.error("Error generating text:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastTextGenerationError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleUsePrompt = (promptToUse: string) => {
    setPrompt(promptToUse);
    toast({ titleKey: "toastInfoTitle", descriptionKey: "toastPromptRestoredSuccess" });
  };

  const handleCopyOutput = async (textToCopy: string) => {
    try {
      await navigator.clipboard.writeText(textToCopy);
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextCopiedSuccess" });
    } catch (error) {
      console.error("Failed to copy text:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastTextCopyError", variant: "destructive" });
    }
  };

  const handleInsertIntoEditor = () => {
    if (generatedOutput) {
      onInsertText(generatedOutput);
      toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextInsertedSuccess" });
    }
  };

  return (
    <Card className="border-none shadow-none">
       <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('aiTextGenerationDescription')}</CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="generation-prompt" className="text-xs text-muted-foreground">{t('yourPromptLabel')}</Label>
            <Input
              id="generation-prompt"
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              placeholder={t('promptPlaceholder')}
              className="mt-1"
              disabled={isLoading}
            />
          </div>
          {generatedOutput && (
            <div>
              <Label htmlFor="generated-text-output" className="text-xs text-muted-foreground">{t('generatedTextLabel')}</Label>
              <Textarea
                id="generated-text-output"
                value={generatedOutput}
                readOnly
                className="mt-1 min-h-[120px] bg-muted font-code"
              />
               <div className="mt-2 flex justify-end">
                <Button 
                  type="button" 
                  variant="outline" 
                  size="sm" 
                  onClick={handleInsertIntoEditor} 
                  disabled={isLoading || !generatedOutput}
                  title={t('insertIntoEditorButtonTooltip')}
                >
                  <ClipboardPaste className="mr-2 h-3.5 w-3.5" />
                  {t('insertIntoEditorButton')}
                </Button>
              </div>
            </div>
          )}
          <Button type="submit" disabled={isLoading} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
            {t('generateTextButton')}
          </Button>
        </form>

        {generationHistory.length > 0 && (
            <Accordion type="single" collapsible className="w-full mt-6">
            <AccordionItem value="generation-history">
                <AccordionTrigger className="text-sm font-medium">
                <div className="flex items-center">
                    <History className="mr-2 h-4 w-4" />
                    {t('generationHistoryTitle')} 
                </div>
                </AccordionTrigger>
                <AccordionContent className="px-1 pb-2 pt-0">
                <div className="space-y-3 max-h-80 overflow-y-auto">
                    {generationHistory.map(item => (
                    <Card key={item.id} className="bg-muted/50">
                        <CardHeader className="p-3">
                        <Label className="text-xs text-muted-foreground">{t('promptLabel')}</Label>
                        <p className="text-xs font-code line-clamp-2">{item.prompt}</p>
                        </CardHeader>
                        <CardContent className="p-3 pt-0">
                        <Label className="text-xs text-muted-foreground">{t('outputLabel')}</Label>
                        <Textarea
                            value={item.generatedText}
                            readOnly
                            className="mt-1 h-20 text-xs font-code bg-background"
                        />
                        </CardContent>
                        <CardFooter className="p-3 pt-0 flex justify-end gap-2">
                        <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => { onInsertText(item.generatedText); toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastTextInsertedSuccess" });}}
                            title={t('insertIntoEditorButtonTooltip')}
                        >
                            <ClipboardPaste className="h-3.5 w-3.5" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleUsePrompt(item.prompt)} title={t('useThisPromptButton')}>
                            <ClipboardEdit className="h-3.5 w-3.5" />
                        </Button>
                        <Button variant="outline" size="sm" onClick={() => handleCopyOutput(item.generatedText)} title={t('copyOutputButton')}>
                            <Copy className="h-3.5 w-3.5" />
                        </Button>
                        </CardFooter>
                    </Card>
                    ))}
                </div>
                </AccordionContent>
            </AccordionItem>
            </Accordion>
        )}
      </CardContent>
    </Card>
  );
}
