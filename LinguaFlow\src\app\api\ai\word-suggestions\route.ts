import { NextRequest, NextResponse } from 'next/server';
import { getWordSuggestions } from '@/ai/flows/word-toolkit-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { word, context, language } = body;

    if (!word || typeof word !== 'string') {
      return NextResponse.json(
        { error: 'Word is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await getWordSuggestions({ 
      word,
      context: context || '',
      language: language || 'en'
    });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in word-suggestions API:', error);
    return NextResponse.json(
      { error: 'Failed to get word suggestions' },
      { status: 500 }
    );
  }
}
