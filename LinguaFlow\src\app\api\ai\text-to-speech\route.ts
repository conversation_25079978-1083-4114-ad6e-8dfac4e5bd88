import { NextRequest, NextResponse } from 'next/server';
import { getSpelledOutAudio } from '@/ai/flows/text-to-speech-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, language, voice } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await getSpelledOutAudio({ 
      text,
      language: language || 'en',
      voice: voice || 'default'
    });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in text-to-speech API:', error);
    return NextResponse.json(
      { error: 'Failed to generate speech' },
      { status: 500 }
    );
  }
}
