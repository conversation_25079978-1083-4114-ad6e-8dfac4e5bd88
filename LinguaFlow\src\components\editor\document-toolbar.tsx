"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useI18n } from '@/contexts/i18n-context';
import { 
  Undo2, 
  Redo2, 
  Save, 
  FileText, 
  Type, 
  Palette,
  ZoomIn,
  ZoomOut,
  RotateCcw
} from 'lucide-react';

interface DocumentToolbarProps {
  onUndo: () => void;
  onRedo: () => void;
  canUndo: boolean;
  canRedo: boolean;
  onSave?: () => void;
  onExport?: () => void;
  fontSize: number;
  onFontSizeChange: (size: number) => void;
  fontFamily: string;
  onFontFamilyChange: (family: string) => void;
  writingMode: string;
  onWritingModeChange: (mode: string) => void;
}

const fontOptions = [
  { value: "'Inter', sans-serif", label: 'Inter' },
  { value: "'Source Code Pro', monospace", label: 'Source Code Pro' },
  { value: "'Georgia', serif", label: 'Georgia' },
  { value: "'Times New Roman', Times, serif", label: 'Times New Roman' },
  { value: "'Arial', sans-serif", label: 'Arial' },
  { value: "'Helvetica', sans-serif", label: 'Helvetica' },
];

const writingModes = [
  { value: 'formal', label: 'Formal' },
  { value: 'casual', label: 'Casual' },
  { value: 'academic', label: 'Academic' },
  { value: 'creative', label: 'Creative' },
  { value: 'business', label: 'Business' },
  { value: 'technical', label: 'Technical' },
];

export function DocumentToolbar({
  onUndo,
  onRedo,
  canUndo,
  canRedo,
  onSave,
  onExport,
  fontSize,
  onFontSizeChange,
  fontFamily,
  onFontFamilyChange,
  writingMode,
  onWritingModeChange,
}: DocumentToolbarProps) {
  const { t } = useI18n();

  const handleFontSizeIncrease = () => {
    if (fontSize < 24) {
      onFontSizeChange(fontSize + 1);
    }
  };

  const handleFontSizeDecrease = () => {
    if (fontSize > 10) {
      onFontSizeChange(fontSize - 1);
    }
  };

  const handleFontSizeReset = () => {
    onFontSizeChange(16);
  };

  return (
    <div className="flex items-center gap-2 p-3 border-b bg-card">
      {/* File Operations */}
      <div className="flex items-center gap-1">
        {onSave && (
          <Button variant="ghost" size="sm" onClick={onSave}>
            <Save className="h-4 w-4 mr-1" />
            {t('saveButton')}
          </Button>
        )}
        {onExport && (
          <Button variant="ghost" size="sm" onClick={onExport}>
            <FileText className="h-4 w-4 mr-1" />
            {t('exportButton')}
          </Button>
        )}
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Undo/Redo */}
      <div className="flex items-center gap-1">
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onUndo} 
          disabled={!canUndo}
          title={t('undoButton')}
        >
          <Undo2 className="h-4 w-4" />
        </Button>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onRedo} 
          disabled={!canRedo}
          title={t('redoButton')}
        >
          <Redo2 className="h-4 w-4" />
        </Button>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Font Controls */}
      <div className="flex items-center gap-2">
        <Type className="h-4 w-4 text-muted-foreground" />
        <Select value={fontFamily} onValueChange={onFontFamilyChange}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {fontOptions.map((font) => (
              <SelectItem key={font.value} value={font.value}>
                <span style={{ fontFamily: font.value }}>{font.label}</span>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <div className="flex items-center gap-1 border rounded-md">
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleFontSizeDecrease}
            disabled={fontSize <= 10}
            className="h-8 w-8 p-0"
          >
            <ZoomOut className="h-3 w-3" />
          </Button>
          <span className="px-2 text-sm font-medium min-w-[2rem] text-center">
            {fontSize}
          </span>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleFontSizeIncrease}
            disabled={fontSize >= 24}
            className="h-8 w-8 p-0"
          >
            <ZoomIn className="h-3 w-3" />
          </Button>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleFontSizeReset}
            className="h-8 w-8 p-0"
            title="Reset font size"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        </div>
      </div>

      <Separator orientation="vertical" className="h-6" />

      {/* Writing Mode */}
      <div className="flex items-center gap-2">
        <Palette className="h-4 w-4 text-muted-foreground" />
        <Select value={writingMode} onValueChange={onWritingModeChange}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            {writingModes.map((mode) => (
              <SelectItem key={mode.value} value={mode.value}>
                {mode.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
}
