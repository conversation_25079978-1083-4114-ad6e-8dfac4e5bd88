
"use client";

// Removed type import - now defined locally
type AnalysisSuggestion = {
  id: string;
  type: 'style' | 'spelling' | 'grammar' | 'rewrite';
  message: string;
  suggestion: string;
  originalSegment: string;
  severity: 'low' | 'medium' | 'high';
  startIndex?: number;
  endIndex?: number;
  suggestions?: string[];
};
import { Button } from '@/components/ui/button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Lightbulb, PenTool, SpellCheck, Info, Check, X } from 'lucide-react';
import { useI18n } from '@/contexts/i18n-context';

interface SuggestionItemProps {
  suggestion: AnalysisSuggestion;
  onApply: (suggestionText: string, originalSegment: string, startIndex?: number, endIndex?: number) => void;
  onDismiss: (suggestionId: string) => void;
}

const getSuggestionTypeAppearance = (type: AnalysisSuggestion['type']): { color: string; icon: JSX.Element; labelKey: string } => {
  switch (type) {
    case 'spelling':
      return { color: 'bg-red-500 hover:bg-red-600', icon: <SpellCheck className="h-3.5 w-3.5 mr-1.5" />, labelKey: 'suggestionTypeSpelling' };
    case 'grammar':
      return { color: 'bg-red-500 hover:bg-red-600', icon: <PenTool className="h-3.5 w-3.5 mr-1.5" />, labelKey: 'suggestionTypeGrammar' };
    case 'rewrite':
      return { color: 'bg-blue-500 hover:bg-blue-600', icon: <Lightbulb className="h-3.5 w-3.5 mr-1.5" />, labelKey: 'suggestionTypeRewrite' };
    case 'style':
      return { color: 'bg-green-500 hover:bg-green-600', icon: <PenTool className="h-3.5 w-3.5 mr-1.5" />, labelKey: 'suggestionTypeStyle' };
    default:
      return { color: 'bg-gray-500 hover:bg-gray-600', icon: <Info className="h-3.5 w-3.5 mr-1.5" />, labelKey: 'suggestionTypeUnknown' };
  }
};

export function SuggestionItem({ suggestion, onApply, onDismiss }: SuggestionItemProps) {
  const { t } = useI18n();
  const { color, icon, labelKey } = getSuggestionTypeAppearance(suggestion.type);

  const handleApply = () => {
    onApply(suggestion.suggestion, suggestion.originalSegment, suggestion.startIndex, suggestion.endIndex);
  };
  
  const handleDismiss = () => {
      onDismiss(suggestion.id);
  };

  return (
    <div className="p-3 border-b last:border-b-0 bg-card hover:bg-muted/50 transition-colors">
      <div className="flex items-start justify-between gap-2">
        <div>
          <div className="flex items-center mb-1">
            <Badge variant="default" className={`text-xs text-white ${color} mr-2`}>
              {icon}
              {t(labelKey)}
            </Badge>
             <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-6 w-6 text-muted-foreground hover:text-foreground">
                    <Info className="h-3.5 w-3.5" />
                    <span className="sr-only">{t('suggestionExplanationTooltip')}</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-72 text-sm p-3" side="top" align="start">
                  {suggestion.message}
                </PopoverContent>
              </Popover>
          </div>
          <p className="text-sm text-muted-foreground mb-1">
            <span className="italic">"{suggestion.originalSegment}"</span>
          </p>
          <p className="text-sm font-semibold text-primary">
            {t('suggestionLabel')}: <span className="font-normal text-foreground">{suggestion.suggestion}</span>
          </p>
        </div>
        <div className="flex items-center gap-1 mt-1 shrink-0">
            <Button onClick={handleApply} size="sm" variant="outline" title={t('correctButton')}>
                <Check className="mr-1.5 h-3.5 w-3.5" />
                {t('correctButton')}
            </Button>
            <Button onClick={handleDismiss} size="icon" variant="ghost" className="h-8 w-8" title={t('dismissButton')}>
                <X className="h-4 w-4" />
            </Button>
        </div>
      </div>
    </div>
  );
}

    