import { NextRequest, NextResponse } from 'next/server';
import { analyzeText } from '@/ai/flows/text-analysis-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, language } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await analyzeText({ 
      text,
      language: language || 'en'
    });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in analyze-text API:', error);
    return NextResponse.json(
      { error: 'Failed to analyze text' },
      { status: 500 }
    );
  }
}
