
"use client";

import { useState, type FormEvent } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { BrainCircuit, Loader2 } from "lucide-react";
// Removed direct import of server action
type AiWritingDetectionOutput = {
  isAiGenerated: boolean;
  confidence: number;
  reasoning: string;
} | null;
import { useToast } from '@/hooks/use-toast';
import { useI18n } from '@/contexts/i18n-context';
import { Progress } from '@/components/ui/progress';

interface AiWritingDetectorProps {
  currentText: string;
}

export function AiWritingDetector({ currentText }: AiWritingDetectorProps) {
  const [analysisResult, setAnalysisResult] = useState<AiWritingDetectionOutput | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { t } = useI18n();

  const handleSubmit = async (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (!currentText.trim()) {
      toast({ titleKey: "toastInputRequiredTitle", descriptionKey: "toastEditorEmptyError", variant: "destructive" });
      return;
    }
    setIsLoading(true);
    setAnalysisResult(null);

    try {
      const response = await fetch('/api/ai/detect-ai-writing', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ text: currentText }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      if (result) {
        setAnalysisResult(result);
        toast({ titleKey: "toastSuccessTitle", descriptionKey: "toastAiWritingDetectionSuccess" });
      } else {
        throw new Error("AI Writing Detection returned no result.");
      }
    } catch (error) {
      console.error("Error detecting AI writing:", error);
      toast({ titleKey: "toastErrorTitle", descriptionKey: "toastAiWritingDetectionError", variant: "destructive" });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="border-none shadow-none">
      <CardHeader className="p-0 pb-4">
        <CardTitle className="text-base">{t('aiWritingDetectionDescription')}</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 p-0">
          {analysisResult && (
            <div className="space-y-3 p-3 bg-muted rounded-md">
              <div>
                <h4 className="font-semibold text-sm">{t('probabilityAIWrittenLabel')}:</h4>
                <div className="flex items-center gap-2 mt-1">
                    <Progress value={analysisResult.probabilityAIWritten} className="w-[calc(100%-4rem)] h-2.5" />
                    <span className="text-sm text-foreground font-medium">{analysisResult.probabilityAIWritten} / 100</span>
                </div>
              </div>
              <div>
                <h4 className="font-semibold text-sm mt-2">{t('aiWritingDetectionSummaryLabel')}:</h4>
                <p className="text-sm text-muted-foreground">{analysisResult.summary}</p>
              </div>
            </div>
          )}
          {!isLoading && !analysisResult && currentText.trim().length === 0 && (
            <p className="text-sm text-muted-foreground text-center py-8">{t('writeSomeTextToDetectAiWritingPlaceholder')}</p>
          )}
        </CardContent>
        <CardFooter className="p-0 pt-4">
          <Button type="submit" disabled={isLoading || currentText.trim().length === 0} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <BrainCircuit className="mr-2 h-4 w-4" />}
            {t('detectAiWritingButton')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
