(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_node-fetch_src_index_fa16faaa.js", {

"[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_node-fetch_src_utils_multipart-parser_ad75e81a.js",
  "static/chunks/node_modules_0d629be2._.js",
  "static/chunks/node_modules_node-fetch_src_index_547aa28b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);