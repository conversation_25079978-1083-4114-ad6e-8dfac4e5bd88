{"version": 3, "sources": [], "sections": [{"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/genkit.ts"], "sourcesContent": ["import {genkit} from 'genkit';\nimport {googleAI} from '@genkit-ai/googleai';\n\nexport const ai = genkit({\n  plugins: [googleAI()],\n  model: 'googleai/gemini-2.0-flash',\n});\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,KAAK,CAAA,GAAA,uIAAA,CAAA,SAAM,AAAD,EAAE;IACvB,SAAS;QAAC,CAAA,GAAA,2KAAA,CAAA,WAAQ,AAAD;KAAI;IACrB,OAAO;AACT", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/config/languages.ts"], "sourcesContent": ["import { Flag } from \"lucide-react\";\n\nexport type ProficiencyLevel = 'native' | 'advanced' | 'intermediate' | 'beginner' | 'none';\n\nexport interface WritingLanguageInfo {\n  value: string; // Base language code e.g., \"en\", \"es\"\n  labelKey: string; // e.g., \"languageEnglishGeneral\"\n  dir: 'ltr' | 'rtl';\n  dialects?: Array<{ value: string; labelKey: string }>; // Full locale code e.g., \"en-US\"\n  supportsProficiency: boolean;\n}\n\nexport const APP_SUPPORTED_UI_LANGUAGES = [\n  { value: \"en-US\", labelKey: \"englishUSLanguage\", dir: \"ltr\" },\n  { value: \"en-GB\", labelKey: \"englishUKLanguage\", dir: \"ltr\" },\n  { value: \"ar\", labelKey: \"arabicLanguage\", dir: \"rtl\" },\n  { value: \"tr\", labelKey: \"turkishLanguage\", dir: \"ltr\" },\n  { value: \"es\", labelKey: \"spanishLanguage\", dir: \"ltr\" },\n  { value: \"de\", labelKey: \"germanLanguage\", dir: \"ltr\" },\n  { value: \"fr\", labelKey: \"frenchLanguage\", dir: \"ltr\" },\n  { value: \"nl\", labelKey: \"dutchLanguage\", dir: \"ltr\" },\n  { value: \"it\", labelKey: \"italianLanguage\", dir: \"ltr\" },\n];\n\nexport const APP_WRITING_LANGUAGES: WritingLanguageInfo[] = [\n  {\n    value: \"en\", labelKey: \"languageEnglishGeneral\", dir: \"ltr\",\n    dialects: [ { value: \"en-US\", labelKey: \"englishUSLanguage\" }, { value: \"en-GB\", labelKey: \"englishUKLanguage\" } ],\n    supportsProficiency: true,\n  },\n  {\n    value: \"es\", labelKey: \"languageSpanishGeneral\", dir: \"ltr\",\n    dialects: [ { value: \"es-ES\", labelKey: \"spanishSpainLanguage\" }, { value: \"es-MX\", labelKey: \"spanishMexicoLanguage\" } ],\n    supportsProficiency: true,\n  },\n  { value: \"fr\", labelKey: \"languageFrenchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"de\", labelKey: \"languageGermanGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"it\", labelKey: \"languageItalianGeneral\", dir: \"ltr\", supportsProficiency: true },\n  { value: \"nl\", labelKey: \"languageDutchGeneral\", dir: \"ltr\", supportsProficiency: true },\n  {\n    value: \"ar\", labelKey: \"languageArabicGeneral\", dir: \"rtl\",\n    dialects: [\n        { value: \"ar-SY\", labelKey: \"arabicSyriaLanguage\" },\n        { value: \"ar-SA\", labelKey: \"arabicSaudiArabiaLanguage\" },\n        { value: \"ar-EG\", labelKey: \"arabicEgyptLanguage\" },\n    ],\n    supportsProficiency: true,\n  },\n  { value: \"tr\", labelKey: \"languageTurkishGeneral\", dir: \"ltr\", supportsProficiency: true },\n];\n\nexport const PROFICIENCY_LEVELS: Array<{value: ProficiencyLevel, labelKey: string}> = [\n    {value: 'native', labelKey: 'proficiencyNative'},\n    {value: 'advanced', labelKey: 'proficiencyAdvanced'},\n    {value: 'intermediate', labelKey: 'proficiencyIntermediate'},\n    {value: 'beginner', labelKey: 'proficiencyBeginner'},\n];\n\n    "], "names": [], "mappings": ";;;;;AAYO,MAAM,6BAA6B;IACxC;QAAE,OAAO;QAAS,UAAU;QAAqB,KAAK;IAAM;IAC5D;QAAE,OAAO;QAAS,UAAU;QAAqB,KAAK;IAAM;IAC5D;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;IACvD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAkB,KAAK;IAAM;IACtD;QAAE,OAAO;QAAM,UAAU;QAAiB,KAAK;IAAM;IACrD;QAAE,OAAO;QAAM,UAAU;QAAmB,KAAK;IAAM;CACxD;AAEM,MAAM,wBAA+C;IAC1D;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YAAE;gBAAE,OAAO;gBAAS,UAAU;YAAoB;YAAG;gBAAE,OAAO;gBAAS,UAAU;YAAoB;SAAG;QAClH,qBAAqB;IACvB;IACA;QACE,OAAO;QAAM,UAAU;QAA0B,KAAK;QACtD,UAAU;YAAE;gBAAE,OAAO;gBAAS,UAAU;YAAuB;YAAG;gBAAE,OAAO;gBAAS,UAAU;YAAwB;SAAG;QACzH,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAAyB,KAAK;QAAO,qBAAqB;IAAK;IACxF;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;IACzF;QAAE,OAAO;QAAM,UAAU;QAAwB,KAAK;QAAO,qBAAqB;IAAK;IACvF;QACE,OAAO;QAAM,UAAU;QAAyB,KAAK;QACrD,UAAU;YACN;gBAAE,OAAO;gBAAS,UAAU;YAAsB;YAClD;gBAAE,OAAO;gBAAS,UAAU;YAA4B;YACxD;gBAAE,OAAO;gBAAS,UAAU;YAAsB;SACrD;QACD,qBAAqB;IACvB;IACA;QAAE,OAAO;QAAM,UAAU;QAA0B,KAAK;QAAO,qBAAqB;IAAK;CAC1F;AAEM,MAAM,qBAAyE;IAClF;QAAC,OAAO;QAAU,UAAU;IAAmB;IAC/C;QAAC,OAAO;QAAY,UAAU;IAAqB;IACnD;QAAC,OAAO;QAAgB,UAAU;IAAyB;IAC3D;QAAC,OAAO;QAAY,UAAU;IAAqB;CACtD", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/language-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect the language of a given text.\n *\n * - detectLanguage - A function that handles the language detection process.\n * - DetectLanguageInput - The input type for the detectLanguage function.\n * - DetectLanguageOutput - The return type for the detectLanguage function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport { APP_WRITING_LANGUAGES } from '@/config/languages';\nimport {z} from 'genkit';\n\nconst DetectLanguageInputSchema = z.object({\n  text: z.string().describe('The text to analyze for language detection.'),\n});\nexport type DetectLanguageInput = z.infer<typeof DetectLanguageInputSchema>;\n\nconst DetectLanguageOutputSchema = z.object({\n  languageCode: z\n    .string()\n    .describe(\n      \"The detected ISO 639-1 language code (e.g., 'en', 'es', 'fr'). Should be 'unknown' if not confident.\"\n    ),\n});\nexport type DetectLanguageOutput = z.infer<typeof DetectLanguageOutputSchema>;\n\nexport async function detectLanguage(input: DetectLanguageInput): Promise<DetectLanguageOutput> {\n  return languageDetectionFlow(input);\n}\n\nconst supportedLanguageCodes = APP_WRITING_LANGUAGES.map(lang => lang.value).join(', ');\n\nconst languageDetectionPrompt = ai.definePrompt({\n  name: 'languageDetectionPrompt',\n  input: {schema: DetectLanguageInputSchema},\n  output: {schema: DetectLanguageOutputSchema},\n  prompt: `Analyze the following text and determine its primary language. Respond with the ISO 639-1 code for the detected language (e.g., 'en', 'es', 'fr'). If the language is not clear or the text is too short, respond with the string 'unknown'. Do not provide any explanation, only the language code or 'unknown'. Supported language codes are: ${supportedLanguageCodes}.\n\nText to analyze:\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n`,\n});\n\nconst languageDetectionFlow = ai.defineFlow(\n  {\n    name: 'languageDetectionFlow',\n    inputSchema: DetectLanguageInputSchema,\n    outputSchema: DetectLanguageOutputSchema,\n  },\n  async (input: DetectLanguageInput): Promise<DetectLanguageOutput> => {\n    try {\n      const {output} = await languageDetectionPrompt(input);\n      if (!output || typeof output.languageCode !== 'string') {\n        const errorMessage = 'AI model did not return the expected languageCode string.';\n        console.error(`[languageDetectionFlow] - ${errorMessage} For input:`, input, 'Output received:', output);\n        return { languageCode: 'unknown' };\n      }\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[languageDetectionFlow] - Error during flow execution for input:`, input, error);\n      return { languageCode: 'unknown' };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AACA;AAAA;;;;;;;AAEA,MAAM,4BAA4B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACzC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,6BAA6B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,cAAc,uIAAA,CAAA,IAAC,CACZ,MAAM,GACN,QAAQ,CACP;AAEN;AAGO,eAAe,uCAAY,GAAZ,eAAe,KAA0B;IAC7D,OAAO,sBAAsB;AAC/B;AAEA,MAAM,yBAAyB,0HAAA,CAAA,wBAAqB,CAAC,GAAG,CAAC,CAAA,OAAQ,KAAK,KAAK,EAAE,IAAI,CAAC;AAElF,MAAM,0BAA0B,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC9C,MAAM;IACN,OAAO;QAAC,QAAQ;IAAyB;IACzC,QAAQ;QAAC,QAAQ;IAA0B;IAC3C,QAAQ,CAAC,gVAAgV,EAAE,uBAAuB;;;;;;AAMpX,CAAC;AACD;AAEA,MAAM,wBAAwB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACzC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,wBAAwB;QAC/C,IAAI,CAAC,UAAU,OAAO,OAAO,YAAY,KAAK,UAAU;YACtD,MAAM,eAAe;YACrB,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,aAAa,WAAW,CAAC,EAAE,OAAO,oBAAoB;YACjG,OAAO;gBAAE,cAAc;YAAU;QACnC;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,gEAAgE,CAAC,EAAE,OAAO;QACzF,OAAO;YAAE,cAAc;QAAU;IACnC;AACF;;;IAvCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-analysis-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent that analyzes text for grammar, spelling, punctuation, style, and clarity, providing suggestions.\n *\n * - analyzeText - A function that handles the text analysis process.\n * - TextAnalysisInput - The input type for the analyzeText function.\n * - TextAnalysisOutput - The return type for the analyzeText function.\n * - AnalysisSuggestion - The structure for individual suggestions.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst AnalysisSuggestionSchema = z.object({\n  id: z.string().describe('A unique identifier for the suggestion.'),\n  type: z\n    .enum(['spelling', 'grammar', 'rewrite', 'style'])\n    .describe(\n      \"The type of issue: 'spelling', 'grammar' (including punctuation), 'rewrite' (for clarity/flow), or 'style'.\"\n    ),\n  message: z.string().describe('A brief, user-friendly explanation of the issue and why the suggestion improves the text.'),\n  suggestion: z.string().describe('The corrected text segment.'),\n  originalSegment: z.string().describe('The original text segment that the suggestion refers to. This helps the user locate the issue if start/end indices are not perfectly accurate or for display purposes.'),\n  startIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based starting character index of the problematic segment in the original text. This can be approximate if the exact segment is hard to determine precisely.'\n    ),\n  endIndex: z\n    .number()\n    .optional()\n    .describe(\n      'The 0-based ending character index (exclusive) of the problematic segment in the original text. This can be approximate.'\n    ),\n});\nexport type AnalysisSuggestion = z.infer<typeof AnalysisSuggestionSchema>;\n\nconst TextAnalysisInputSchema = z.object({\n  text: z.string().describe('The text to analyze.'),\n  language: z.string().describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\", \"ar\"). This helps tailor the analysis to the specific language.'),\n});\nexport type TextAnalysisInput = z.infer<typeof TextAnalysisInputSchema>;\n\nconst TextAnalysisOutputSchema = z.object({\n  suggestions: z\n    .array(AnalysisSuggestionSchema)\n    .describe('A list of suggestions for the input text.'),\n});\nexport type TextAnalysisOutput = z.infer<typeof TextAnalysisOutputSchema>;\n\nexport async function analyzeText(\n  input: TextAnalysisInput\n): Promise<TextAnalysisOutput> {\n  return textAnalysisFlow(input);\n}\n\nconst textAnalysisPrompt = ai.definePrompt({\n  name: 'textAnalysisPrompt',\n  input: {schema: TextAnalysisInputSchema},\n  output: {schema: TextAnalysisOutputSchema},\n  prompt: `You are an expert writing assistant. Your goal is to help users make their writing clear, natural, and human-sounding. The text you will analyze is in the language specified by the 'language' input field (e.g., 'en' for English, 'es' for Spanish, 'ar' for Arabic).\nYour task is to identify issues and provide suggestions for correction tailored to the grammar, spelling, punctuation, style, and clarity rules of that specific language ({{language}}).\n\nFor each issue you find, you MUST provide:\n1.  \\`id\\`: A unique string identifier for this specific suggestion (e.g., \"suggestion-1\", \"suggestion-2\").\n2.  \\`type\\`: The category of the issue. Must be one of:\n    *   'spelling': For misspelled words.\n    *   'grammar': For grammatical and punctuation errors (e.g., subject-verb agreement, tense, articles, comma usage, apostrophes).\n    *   'rewrite': For sentences that are grammatically correct but could be rewritten for better flow and impact. Focus on varying sentence structure, improving transitions, and ensuring the new phrasing connects logically with the surrounding text. Only suggest a rewrite if it provides a significant improvement; do not suggest rewrites for sentences that are already well-structured.\n    *   'style': Your primary focus for style is to enhance vocabulary by replacing weak, generic, or repeated verbs with more dynamic, vivid, and engaging alternatives. Introduce natural-sounding phrasal verbs where appropriate to make the text less robotic. Only provide 'style' suggestions for verbs. For example, instead of \"walked quickly,\" suggest \"dashed\"; instead of repeating \"said,\" suggest \"murmured\" or \"exclaimed.\"\n3.  \\`message\\`: A brief, user-friendly explanation of the issue and why your suggestion improves the text.\n4.  \\`suggestion\\`: The corrected or improved text segment.\n5.  \\`originalSegment\\`: The exact original text segment that this suggestion pertains to. This is crucial for the user to understand the context.\n6.  \\`startIndex\\` (optional): The 0-based starting character index of the 'originalSegment' in the *entire* provided text. This can be approximate if the exact segment is hard to determine precisely.\n7.  \\`endIndex\\` (optional): The 0-based ending character index (exclusive) of the 'originalSegment' in the *entire* provided text. This can be approximate.\n\nImportant Guidelines:\n- Focus on providing actionable and clear suggestions that make the writing feel more natural and human.\n- The 'style' and 'rewrite' suggestions are very important. Actively look for opportunities to make the language more powerful, engaging, and less robotic.\n- For 'rewrite' and 'style' suggestions, ensure the \\`originalSegment\\` captures enough context (e.g., a full sentence for rewrites, or a specific word/phrase for style).\n- Be conservative with \\`startIndex\\` and \\`endIndex\\`. If you cannot determine them with high confidence, it's better to rely on \\`originalSegment\\`.\n- If the text is perfect and has no issues, return an empty array for 'suggestions'.\n\nAnalyze the following text (language: {{language}}):\n\\`\\`\\`\n{{{text}}}\n\\`\\`\\`\n\nRespond with a JSON object containing a 'suggestions' array.\nExample for a single suggestion:\n{\n  \"suggestions\": [\n    {\n      \"id\": \"s1\",\n      \"type\": \"spelling\",\n      \"message\": \"'Helo' appears to be a misspelling of 'Hello'.\",\n      \"suggestion\": \"Hello\",\n      \"originalSegment\": \"Helo\",\n      \"startIndex\": 0,\n      \"endIndex\": 4\n    }\n  ]\n}\nIf multiple issues, add more objects to the 'suggestions' array.\n`,\n});\n\nconst textAnalysisFlow = ai.defineFlow(\n  {\n    name: 'textAnalysisFlow',\n    inputSchema: TextAnalysisInputSchema,\n    outputSchema: TextAnalysisOutputSchema,\n  },\n  async (input: TextAnalysisInput): Promise<TextAnalysisOutput> => {\n    if (!input.text.trim()) {\n      return {suggestions: []};\n    }\n    try {\n      const {output} = await textAnalysisPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      // We still handle the case where the model returns an empty suggestions array.\n      const suggestionsWithUniqueIds = (output?.suggestions || []).map((s, index) => ({\n        ...s,\n        id: s.id || `suggestion-${Date.now()}-${index}`, // Ensure unique ID\n      }));\n      return { suggestions: suggestionsWithUniqueIds };\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[textAnalysisFlow] - Error: ${errorMessage}`, {input});\n       // For background tasks like real-time analysis, it's better to fail gracefully\n      // and return an empty result than to crash the UI with an error for a transient issue.\n      return { suggestions: [] };\n    }\n  }\n);\n\n    "], "names": [], "mappings": ";;;;;AAEA;;;;;;;CAOC,GAED;AACA;;;;;;AAEA,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,IAAI,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACxB,MAAM,oIAAA,CAAA,IAAC,CACJ,IAAI,CAAC;QAAC;QAAY;QAAW;QAAW;KAAQ,EAChD,QAAQ,CACP;IAEJ,SAAS,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC7B,YAAY,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAChC,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACrC,YAAY,oIAAA,CAAA,IAAC,CACV,MAAM,GACN,QAAQ,GACR,QAAQ,CACP;IAEJ,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,GACR,QAAQ,CACP;AAEN;AAGA,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAChC;AAGA,MAAM,2BAA2B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,aAAa,oIAAA,CAAA,IAAC,CACX,KAAK,CAAC,0BACN,QAAQ,CAAC;AACd;AAGO,eAAe,uCAAS,GAAT,YACpB,KAAwB;IAExB,OAAO,iBAAiB;AAC1B;AAEA,MAAM,qBAAqB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACzC,MAAM;IACN,OAAO;QAAC,QAAQ;IAAuB;IACvC,QAAQ;QAAC,QAAQ;IAAwB;IACzC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CX,CAAC;AACD;AAEA,MAAM,mBAAmB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACpC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,IAAI;QACtB,OAAO;YAAC,aAAa,EAAE;QAAA;IACzB;IACA,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,mBAAmB;QAC1C,8EAA8E;QAC9E,+EAA+E;QAC/E,+EAA+E;QAC/E,MAAM,2BAA2B,CAAC,QAAQ,eAAe,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,QAAU,CAAC;gBAC9E,GAAG,CAAC;gBACJ,IAAI,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,OAAO;YACjD,CAAC;QACD,OAAO;YAAE,aAAa;QAAyB;IACjD,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,cAAc,EAAE;YAAC;QAAK;QAClE,+EAA+E;QAChF,uFAAuF;QACvF,OAAO;YAAE,aAAa,EAAE;QAAC;IAC3B;AACF;;;IApFoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-tone-analysis.ts"], "sourcesContent": ["'use server';\n\n/**\n * @fileOverview An AI agent to analyze the tone of writing and provide feedback.\n *\n * - analyzeTone - A function that handles the tone analysis process.\n * - AnalyzeToneInput - The input type for the analyzeTone function.\n * - AnalyzeToneOutput - The return type for the analyzeTone function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst AnalyzeToneInputSchema = z.object({\n  text: z.string().describe('The text to analyze for tone.'),\n});\nexport type AnalyzeToneInput = z.infer<typeof AnalyzeToneInputSchema>;\n\nconst AnalyzeToneOutputSchema = z.object({\n  formality: z\n    .string()\n    .describe(\n      'The formality level of the text (e.g., formal, informal, neutral).'\n    ),\n  confidence: z\n    .string()\n    .describe('The confidence level of the text (e.g., confident, tentative).'),\n  feedback: z.string().describe('Feedback on how to adjust the writing style.'),\n});\nexport type AnalyzeToneOutput = z.infer<typeof AnalyzeToneOutputSchema>;\n\nexport async function analyzeTone(input: AnalyzeToneInput): Promise<AnalyzeToneOutput | null> {\n  return analyzeToneFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'analyzeTonePrompt',\n  input: {schema: AnalyzeToneInputSchema},\n  output: {schema: AnalyzeToneOutputSchema},\n  prompt: `You are an AI writing assistant that analyzes the tone of the given text and provides feedback on its formality and confidence levels.\n\nAnalyze the following text:\n\n{{{text}}}\n\nProvide the formality level, confidence level, and feedback on how to adjust the writing style to match the intended audience and purpose. Be concise.\n\nFormality:\nConfidence:\nFeedback:`,\n});\n\nconst analyzeToneFlow = ai.defineFlow(\n  {\n    name: 'analyzeToneFlow',\n    inputSchema: AnalyzeToneInputSchema,\n    outputSchema: AnalyzeToneOutputSchema.nullable(),\n  },\n  async (input: AnalyzeToneInput): Promise<AnalyzeToneOutput | null> => {\n    try {\n      const {output} = await prompt(input);\n       // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[analyzeToneFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,yBAAyB,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,0BAA0B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,WAAW,uIAAA,CAAA,IAAC,CACT,MAAM,GACN,QAAQ,CACP;IAEJ,YAAY,uIAAA,CAAA,IAAC,CACV,MAAM,GACN,QAAQ,CAAC;IACZ,UAAU,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAChC;AAGO,eAAe,uCAAS,GAAT,YAAY,KAAuB;IACvD,OAAO,gBAAgB;AACzB;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAsB;IACtC,QAAQ;QAAC,QAAQ;IAAuB;IACxC,QAAQ,CAAC;;;;;;;;;;SAUF,CAAC;AACV;AAEA,MAAM,kBAAkB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACnC;IACE,MAAM;IACN,aAAa;IACb,cAAc,wBAAwB,QAAQ;AAChD,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;QAC7B,8EAA8E;QAC/E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,cAAc,EAAE;YAAC;QAAK;QAClE,2CAA2C;QAC3C,OAAO;IACT;AACF;;;IAvCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 699, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-text-generation.ts"], "sourcesContent": ["// This is an autogenerated file from Firebase Studio.\n\n'use server';\n\n/**\n * @fileOverview A flow for generating text content based on a user-provided prompt.\n *\n * - generateText - A function that takes a prompt and returns generatedText.\n * - GenerateTextInput - The input type for the generateText function.\n * - GenerateTextOutput - The return type for the generateText function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst GenerateTextInputSchema = z.object({\n  prompt: z.string().describe('The prompt to generate text from.'),\n});\nexport type GenerateTextInput = z.infer<typeof GenerateTextInputSchema>;\n\nconst GenerateTextOutputSchema = z.object({\n  generatedText: z.string().describe('The generated text content.'),\n});\nexport type GenerateTextOutput = z.infer<typeof GenerateTextOutputSchema>;\n\nexport async function generateText(input: GenerateTextInput): Promise<GenerateTextOutput> {\n  return aiTextGenerationFlow(input);\n}\n\nconst aiTextGenerationPrompt = ai.definePrompt({\n  name: 'aiTextGenerationPrompt',\n  input: {schema: GenerateTextInputSchema},\n  output: {schema: GenerateTextOutputSchema},\n  prompt: `Generate text content based on the following prompt:\\n\\n{{{prompt}}}`,\n});\n\nconst aiTextGenerationFlow = ai.defineFlow(\n  {\n    name: 'aiTextGenerationFlow',\n    inputSchema: GenerateTextInputSchema,\n    outputSchema: GenerateTextOutputSchema,\n  },\n  async (input: GenerateTextInput): Promise<GenerateTextOutput> => {\n    try {\n      const {output} = await aiTextGenerationPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[aiTextGenerationFlow] - Error: ${errorMessage}`, {input});\n      // Re-throw the error to be handled by the calling UI component\n      throw new Error(`AI text generation failed: ${errorMessage}`);\n    }\n  }\n);\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;;AAItD;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,0BAA0B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,QAAQ,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC9B;AAGA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACrC;AAGO,eAAe,uCAAU,GAAV,aAAa,KAAwB;IACzD,OAAO,qBAAqB;AAC9B;AAEA,MAAM,yBAAyB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7C,MAAM;IACN,OAAO;QAAC,QAAQ;IAAuB;IACvC,QAAQ;QAAC,QAAQ;IAAwB;IACzC,QAAQ,CAAC,oEAAoE,CAAC;AAChF;AAEA,MAAM,uBAAuB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,uBAAuB;QAC9C,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,cAAc,EAAE;YAAC;QAAK;QACvE,+DAA+D;QAC/D,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,cAAc;IAC9D;AACF;;;IA7BoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/contextual-ai-rephraser.ts"], "sourcesContent": ["'use server';\n/**\n * @fileOverview An AI agent that rephrases text based on context.\n *\n * - rephraseText - A function that handles the text rephrasing process.\n * - RephraseTextInput - The input type for the rephraseText function.\n * - RephraseTextOutput - The return type for the rephraseText function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst RephraseTextInputSchema = z.object({\n  selectedText: z.string().describe('The text selected by the user to rephrase.'),\n  contextText: z.string().describe('The surrounding context of the selected text.'),\n  tone: z.string().optional().describe('The desired tone of the rephrased text.'),\n  style: z.string().optional().describe('The desired style of the rephrased text.'),\n});\nexport type RephraseTextInput = z.infer<typeof RephraseTextInputSchema>;\n\nconst RephraseTextOutputSchema = z.object({\n  rephrasedText: z.string().describe('The rephrased text based on the context.'),\n});\nexport type RephraseTextOutput = z.infer<typeof RephraseTextOutputSchema>;\n\nexport async function rephraseText(input: RephraseTextInput): Promise<RephraseTextOutput | null> {\n  return rephraseTextFlow(input);\n}\n\nconst prompt = ai.definePrompt({\n  name: 'rephraseTextPrompt',\n  input: {schema: RephraseTextInputSchema},\n  output: {schema: RephraseTextOutputSchema},\n  prompt: `You are an AI assistant that helps users rephrase text to improve clarity and flow.\n\n  Selected Text: {{{selectedText}}}\n  Context: {{{contextText}}}\n  Tone: {{{tone}}}\n  Style: {{{style}}}\n\n  Rephrased Text:`,\n});\n\nconst rephraseTextFlow = ai.defineFlow(\n  {\n    name: 'rephraseTextFlow',\n    inputSchema: RephraseTextInputSchema,\n    outputSchema: RephraseTextOutputSchema.nullable(),\n  },\n  async (input: RephraseTextInput): Promise<RephraseTextOutput | null> => {\n    try {\n      const {output} = await prompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[rephraseTextFlow] - Error: ${errorMessage}`, {input});\n      // Return null on error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AACA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,0BAA0B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAClC,aAAa,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACjC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;IACrC,OAAO,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AACxC;AAGA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,eAAe,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACrC;AAGO,eAAe,uCAAU,GAAV,aAAa,KAAwB;IACzD,OAAO,iBAAiB;AAC1B;AAEA,MAAM,SAAS,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC7B,MAAM;IACN,OAAO;QAAC,QAAQ;IAAuB;IACvC,QAAQ;QAAC,QAAQ;IAAwB;IACzC,QAAQ,CAAC;;;;;;;iBAOM,CAAC;AAClB;AAEA,MAAM,mBAAmB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACpC;IACE,MAAM;IACN,aAAa;IACb,cAAc,yBAAyB,QAAQ;AACjD,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,OAAO;QAC9B,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,cAAc,EAAE;YAAC;QAAK;QACnE,uBAAuB;QACvB,OAAO;IACT;AACF;;;IApCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/plagiarism-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect potential plagiarism in text.\n *\n * - detectPlagiarism - A function that handles the plagiarism detection process.\n * - PlagiarismDetectionInput - The input type for the detectPlagiarism function.\n * - PlagiarismDetectionOutput - The return type for the detectPlagiarism function.\n */\n\nimport {ai}from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst PlagiarismDetectionInputSchema = z.object({\n  text: z.string().describe('The text to analyze for plagiarism.'),\n});\nexport type PlagiarismDetectionInput = z.infer<typeof PlagiarismDetectionInputSchema>;\n\nconst PlagiarizedSegmentSchema = z.object({\n  plagiarizedSegment: z.string().describe('The exact text segment from the input that is potentially plagiarized.'),\n  originalSource: z.string().describe('The URL or a clear citation of the original source from which the content was likely derived.'),\n  similarityScore: z.number().min(0).max(100).describe('A score from 0 to 100 indicating the similarity to the original source.'),\n   startIndex: z\n    .number()\n    .describe(\n      'The 0-based starting character index of the plagiarized segment in the original text. This is mandatory.'\n    ),\n  endIndex: z\n    .number()\n    .describe(\n      'The 0-based ending character index (exclusive) of the plagiarized segment in the original text. This is mandatory.'\n    ),\n});\n\nconst PlagiarismDetectionOutputSchema = z.object({\n  originalityScore: z.number().min(0).max(100).describe(\"A score from 0 to 100, where 100 indicates completely original content and 0 indicates a high likelihood of plagiarism. This score should reflect the percentage of the text that appears to be original.\"),\n  detectedSources: z.array(PlagiarizedSegmentSchema).describe('A list of detected sources of potential plagiarism. If no plagiarism is found, this should be an empty array.'),\n  analysisReport: z.string().describe(\"A concise report summarizing the plagiarism analysis. Highlight any specific phrases or sentences that appear to be unoriginal, or confirm the text's originality if no issues are found. Keep this report brief, under 100 words.\"),\n});\nexport type PlagiarismDetectionOutput = z.infer<typeof PlagiarismDetectionOutputSchema>;\n\nexport async function detectPlagiarism(input: PlagiarismDetectionInput): Promise<PlagiarismDetectionOutput | null> {\n  return plagiarismDetectionFlow(input);\n}\n\nconst plagiarismDetectionPrompt = ai.definePrompt({\n  name: 'plagiarismDetectionPrompt',\n  input: {schema: PlagiarismDetectionInputSchema},\n  output: {schema: PlagiarismDetectionOutputSchema},\n  prompt: `You are an AI assistant specialized in detecting plagiarism in written text by comparing it against a vast database of existing works.\n\nYour task is to analyze the provided text for originality. For the given text, you must:\n1.  Calculate an overall 'originalityScore' from 0 to 100, where 100 means completely original and 0 indicates high plagiarism.\n2.  Identify specific sentences or paragraphs that appear to be copied or heavily paraphrased from other sources.\n3.  For each identified segment, you MUST provide the 'plagiarizedSegment', its 'startIndex' and 'endIndex' in the original text, the 'originalSource' (a URL or book citation), and a 'similarityScore' (0-100). The 'startIndex' and 'endIndex' are mandatory and must be accurate.\n4.  Provide a brief 'analysisReport' (under 100 words) summarizing your findings.\n5.  If you detect potential plagiarism, populate the 'detectedSources' array. If the text is original, the 'detectedSources' array must be empty.\n\nAnalyze the following text:\n{{{text}}}\n`,\n});\n\nconst plagiarismDetectionFlow = ai.defineFlow(\n  {\n    name: 'plagiarismDetectionFlow',\n    inputSchema: PlagiarismDetectionInputSchema,\n    outputSchema: PlagiarismDetectionOutputSchema.nullable(),\n  },\n  async (input: PlagiarismDetectionInput): Promise<PlagiarismDetectionOutput | null> => {\n    try {\n      const {output} = await plagiarismDetectionPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[plagiarismDetectionFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,iCAAiC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,oBAAoB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACxC,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IACpC,iBAAiB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ,CAAC;IACpD,YAAY,uIAAA,CAAA,IAAC,CACX,MAAM,GACN,QAAQ,CACP;IAEJ,UAAU,uIAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,CACP;AAEN;AAEA,MAAM,kCAAkC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC/C,kBAAkB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,KAAK,QAAQ,CAAC;IACtD,iBAAiB,uIAAA,CAAA,IAAC,CAAC,KAAK,CAAC,0BAA0B,QAAQ,CAAC;IAC5D,gBAAgB,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AACtC;AAGO,eAAe,uCAAc,GAAd,iBAAiB,KAA+B;IACpE,OAAO,wBAAwB;AACjC;AAEA,MAAM,4BAA4B,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAChD,MAAM;IACN,OAAO;QAAC,QAAQ;IAA8B;IAC9C,QAAQ;QAAC,QAAQ;IAA+B;IAChD,QAAQ,CAAC;;;;;;;;;;;AAWX,CAAC;AACD;AAEA,MAAM,0BAA0B,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC3C;IACE,MAAM;IACN,aAAa;IACb,cAAc,gCAAgC,QAAQ;AACxD,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,0BAA0B;QACjD,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,cAAc,EAAE;YAAC;QAAK;QAC1E,2CAA2C;QAC3C,OAAO;IACT;AACF;;;IAxCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 934, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/ai-writing-detection-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to detect if text was likely written by AI.\n *\n * - detectAiWriting - A function that handles the AI writing detection process.\n * - AiWritingDetectionInput - The input type for the detectAiWriting function.\n * - AiWritingDetectionOutput - The return type for the detectAiWriting function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst AiWritingDetectionInputSchema = z.object({\n  text: z.string().describe('The text to analyze for AI authorship.'),\n});\nexport type AiWritingDetectionInput = z.infer<typeof AiWritingDetectionInputSchema>;\n\nconst AiWritingDetectionOutputSchema = z.object({\n  probabilityAIWritten: z\n    .number()\n    .min(0)\n    .max(100)\n    .describe(\n      'A score from 0 to 100 indicating the likelihood that the text was written by AI. 100 means highly likely AI-generated, 0 means highly likely human-written.'\n    ),\n  summary: z\n    .string()\n    .describe(\n      \"A brief summary of the AI writing detection analysis. Explain the score and any notable characteristics found. Keep this summary concise, under 100 words.\"\n    ),\n});\nexport type AiWritingDetectionOutput = z.infer<typeof AiWritingDetectionOutputSchema>;\n\nexport async function detectAiWriting(input: AiWritingDetectionInput): Promise<AiWritingDetectionOutput | null> {\n  return aiWritingDetectionFlow(input);\n}\n\nconst aiWritingDetectionPrompt = ai.definePrompt({\n  name: 'aiWritingDetectionPrompt',\n  input: {schema: AiWritingDetectionInputSchema},\n  output: {schema: AiWritingDetectionOutputSchema},\n  prompt: `You are an expert in detecting AI-generated text. Analyze the provided text and determine the probability that it was written by an AI.\nYour task is to evaluate the text and provide a 'probabilityAIWritten' score between 0 and 100, where 100 indicates a high likelihood of AI generation and 0 indicates a high likelihood of human authorship.\nAlso, provide a brief 'summary' (under 100 words) explaining your reasoning and any stylistic indicators you found.\n\nAnalyze the following text:\n{{{text}}}\n`,\n});\n\nconst aiWritingDetectionFlow = ai.defineFlow(\n  {\n    name: 'aiWritingDetectionFlow',\n    inputSchema: AiWritingDetectionInputSchema,\n    outputSchema: AiWritingDetectionOutputSchema.nullable(),\n  },\n  async (input: AiWritingDetectionInput): Promise<AiWritingDetectionOutput | null> => {\n    try {\n      const {output} = await aiWritingDetectionPrompt(input);\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[aiWritingDetectionFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error to prevent server crashes on API failures.\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,gCAAgC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7C,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,iCAAiC,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,sBAAsB,uIAAA,CAAA,IAAC,CACpB,MAAM,GACN,GAAG,CAAC,GACJ,GAAG,CAAC,KACJ,QAAQ,CACP;IAEJ,SAAS,uIAAA,CAAA,IAAC,CACP,MAAM,GACN,QAAQ,CACP;AAEN;AAGO,eAAe,uCAAa,GAAb,gBAAgB,KAA8B;IAClE,OAAO,uBAAuB;AAChC;AAEA,MAAM,2BAA2B,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IAC/C,MAAM;IACN,OAAO;QAAC,QAAQ;IAA6B;IAC7C,QAAQ;QAAC,QAAQ;IAA8B;IAC/C,QAAQ,CAAC;;;;;;AAMX,CAAC;AACD;AAEA,MAAM,yBAAyB,mHAAA,CAAA,KAAE,CAAC,UAAU,CAC1C;IACE,MAAM;IACN,aAAa;IACb,cAAc,+BAA+B,QAAQ;AACvD,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,yBAAyB;QAChD,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,cAAc,EAAE;YAAC;QAAK;QACzE,sFAAsF;QACtF,OAAO;IACT;AACF;;;IAjCoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1007, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/humanize-ai-text-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to rewrite AI-generated text into a more human-like style.\n *\n * - humanizeText - A function that handles the text humanization process.\n * - HumanizeTextInput - The input type for the humanizeText function.\n * - HumanizeTextOutput - The return type for the humanizeText function.\n */\n\nimport {ai}from '@/ai/genkit';\nimport {z}from 'genkit';\n\nconst HumanizeTextInputSchema = z.object({\n  text: z.string().describe('The AI-generated text to be humanized.'),\n});\nexport type HumanizeTextInput = z.infer<typeof HumanizeTextInputSchema>;\n\nconst HumanizeTextOutputSchema = z.object({\n  humanizedText: z\n    .string()\n    .describe('The text rewritten in a more human-like style.'),\n});\nexport type HumanizeTextOutput = z.infer<typeof HumanizeTextOutputSchema>;\n\nexport async function humanizeText(input: HumanizeTextInput): Promise<HumanizeTextOutput | null> {\n  return humanizeTextFlow(input);\n}\n\nconst humanizeTextPrompt = ai.definePrompt({\n  name: 'humanizeTextPrompt',\n  input: {schema: HumanizeTextInputSchema},\n  output: {schema: HumanizeTextOutputSchema},\n  prompt: `You are an expert editor specializing in transforming AI-generated text into writing that is clear, engaging, and indistinguishable from a skilled human author. Your task is to rewrite the provided text, focusing on the following principles to ensure it resonates with a natural, human touch, leaving no room for confusion.\n\n1.  **Enhance Readability and Flow:**\n    *   **Paragraph Structure:** Reorganize paragraphs if necessary to improve the logical progression of ideas. Ensure each paragraph focuses on a single, clear concept.\n    *   **Sentence Variety:** Eliminate monotonous sentence structures. Employ a mix of short, punchy sentences for emphasis and longer, more complex sentences to elaborate on ideas. Avoid repetitive sentence beginnings.\n    *   **Smooth Transitions:** Ensure seamless transitions between sentences and paragraphs. Use transition words and phrases naturally, avoiding clichés or predictable patterns. The goal is a smooth, logical flow from one idea to the next.\n\n2.  **Adopt a Natural, Human Voice:**\n    *   **Word Choice:** Replace robotic or overly formal vocabulary with more common, natural-sounding language. Use vivid verbs, concrete nouns, and relatable analogies where appropriate.\n    *   **Use Contractions:** Integrate contractions (e.g., \"it's,\" \"don't,\" \"you'll\") where they fit naturally to create a more conversational and approachable tone.\n    *   **Active Voice:** Strongly favor the active voice over the passive voice to make the writing more direct, energetic, and clear.\n\n3.  **Eliminate AI Hallmarks:**\n    *   **Cut Redundancy:** Aggressively remove filler words, boilerplate phrases, and repetitive statements often found in AI text (e.g., \"In conclusion,\" \"It is important to note,\" \"Moreover,\" \"Furthermore,\" \"In the world of...\").\n    *   **Be Direct and Concise:** Get straight to the point. Avoid unnecessary preambles or summaries unless they are essential to the text's purpose. Make every word count.\n\n4.  **Preserve the Core Message:**\n    *   **Accuracy is Paramount:** It is critical that you enhance the *style* and *readability* without altering the original meaning, key facts, or critical information of the text. Your rewrite must remain factually identical to the source.\n\nYour final output must ONLY be the rewritten, humanized text. Do not include any notes, explanations, apologies, or conversational filler.\n\nRewrite the following text:\n{{{text}}}\n`,\n});\n\nconst humanizeTextFlow = ai.defineFlow(\n  {\n    name: 'humanizeTextFlow',\n    inputSchema: HumanizeTextInputSchema,\n    outputSchema: HumanizeTextOutputSchema.nullable(),\n  },\n  async (input: HumanizeTextInput): Promise<HumanizeTextOutput | null> => {\n    try {\n      const {output} = await humanizeTextPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output;\n    } catch (error) {\n      const errorMessage = error instanceof Error ? error.message : String(error);\n      console.error(`[humanizeTextFlow] - Error: ${errorMessage}`, {input});\n      // Return null instead of throwing an error\n      return null;\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;;;;;;AAEA,MAAM,0BAA0B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,2BAA2B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,eAAe,uIAAA,CAAA,IAAC,CACb,MAAM,GACN,QAAQ,CAAC;AACd;AAGO,eAAe,uCAAU,GAAV,aAAa,KAAwB;IACzD,OAAO,iBAAiB;AAC1B;AAEA,MAAM,qBAAqB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACzC,MAAM;IACN,OAAO;QAAC,QAAQ;IAAuB;IACvC,QAAQ;QAAC,QAAQ;IAAwB;IACzC,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAuBX,CAAC;AACD;AAEA,MAAM,mBAAmB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACpC;IACE,MAAM;IACN,aAAa;IACb,cAAc,yBAAyB,QAAQ;AACjD,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,mBAAmB;QAC1C,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,cAAc,EAAE;YAAC;QAAK;QACnE,2CAA2C;QAC3C,OAAO;IACT;AACF;;;IApDoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1098, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/word-toolkit-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to provide synonyms and spelling for a word.\n *\n * - getWordSuggestions - A function that handles the word analysis process.\n * - WordToolkitInput - The input type for the getWordSuggestions function.\n * - WordToolkitOutput - The return type for the getWordSuggestions function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {z} from 'zod';\n\nconst WordToolkitInputSchema = z.object({\n  word: z.string().describe('The single word to be analyzed.'),\n  context: z\n    .string()\n    .describe('The surrounding sentence or text to provide context.'),\n  language: z\n    .string()\n    .describe('The ISO 639-1 language code of the text (e.g., \"en\", \"es\").'),\n});\nexport type WordToolkitInput = z.infer<typeof WordToolkitInputSchema>;\n\nconst WordToolkitOutputSchema = z.object({\n  synonyms: z\n    .array(z.string())\n    .describe('An array of relevant synonyms for the word, based on its context. Should be 5 or less.'),\n  correctSpelling: z\n    .string()\n    .describe(\n      'The correct spelling of the word. If the word is already spelled correctly, returns the original word.'\n    ),\n});\nexport type WordToolkitOutput = z.infer<typeof WordToolkitOutputSchema>;\n\nexport async function getWordSuggestions(\n  input: WordToolkitInput\n): Promise<WordToolkitOutput> {\n  return wordToolkitFlow(input);\n}\n\nconst wordToolkitPrompt = ai.definePrompt({\n  name: 'wordToolkitPrompt',\n  input: {schema: WordToolkitInputSchema},\n  output: {schema: WordToolkitOutputSchema},\n  prompt: `You are a linguistic expert providing quick tools for writers. You will be given a specific word, its surrounding context, and its language.\n\nYour task is to provide a list of synonyms and the correct spelling for the given word in the specified language ({{language}}).\n\n- Synonyms should be relevant to the word's usage in the provided context. Provide up to 5 synonyms. If no relevant synonyms are found, return an empty array.\n- For spelling, if the word is already spelled correctly, return the word itself. If it is misspelled, return the correct spelling.\n\nAnalyze the following:\nWord: {{{word}}}\nContext: {{{context}}}\nLanguage: {{language}}\n`,\n});\n\nconst wordToolkitFlow = ai.defineFlow(\n  {\n    name: 'wordToolkitFlow',\n    inputSchema: WordToolkitInputSchema,\n    outputSchema: WordToolkitOutputSchema,\n  },\n  async (input: WordToolkitInput): Promise<WordToolkitOutput> => {\n    try {\n      const {output} = await wordToolkitPrompt(input);\n      // The Zod schema validation on the prompt handles the output structure check.\n      // If the output is invalid, Genkit will throw an error, which is caught below.\n      return output!;\n    } catch (error) {\n      const errorMessage =\n        error instanceof Error ? error.message : String(error);\n      console.error(`[wordToolkitFlow] - Error: ${errorMessage}`, {input});\n      // Return a default/empty state to prevent the client from crashing\n      // on transient errors like API quota limits.\n      return {\n        synonyms: [],\n        correctSpelling: input.word,\n      };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;;;;;;AAEA,MAAM,yBAAyB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,SAAS,oIAAA,CAAA,IAAC,CACP,MAAM,GACN,QAAQ,CAAC;IACZ,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,QAAQ,CAAC;AACd;AAGA,MAAM,0BAA0B,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,UAAU,oIAAA,CAAA,IAAC,CACR,KAAK,CAAC,oIAAA,CAAA,IAAC,CAAC,MAAM,IACd,QAAQ,CAAC;IACZ,iBAAiB,oIAAA,CAAA,IAAC,CACf,MAAM,GACN,QAAQ,CACP;AAEN;AAGO,eAAe,uCAAgB,GAAhB,mBACpB,KAAuB;IAEvB,OAAO,gBAAgB;AACzB;AAEA,MAAM,oBAAoB,mHAAA,CAAA,KAAE,CAAC,YAAY,CAAC;IACxC,MAAM;IACN,OAAO;QAAC,QAAQ;IAAsB;IACtC,QAAQ;QAAC,QAAQ;IAAuB;IACxC,QAAQ,CAAC;;;;;;;;;;;AAWX,CAAC;AACD;AAEA,MAAM,kBAAkB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACnC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO;IACL,IAAI;QACF,MAAM,EAAC,MAAM,EAAC,GAAG,MAAM,kBAAkB;QACzC,8EAA8E;QAC9E,+EAA+E;QAC/E,OAAO;IACT,EAAE,OAAO,OAAO;QACd,MAAM,eACJ,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QAClD,QAAQ,KAAK,CAAC,CAAC,2BAA2B,EAAE,cAAc,EAAE;YAAC;QAAK;QAClE,mEAAmE;QACnE,6CAA6C;QAC7C,OAAO;YACL,UAAU,EAAE;YACZ,iBAAiB,MAAM,IAAI;QAC7B;IACF;AACF;;;IA/CoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1207, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/ai/flows/text-to-speech-flow.ts"], "sourcesContent": ["\n'use server';\n/**\n * @fileOverview An AI agent to convert a word into spoken audio of its spelling.\n *\n * - getSpelledOutAudio - A function that takes a word and generates a WAV audio data URI of it being spelled out.\n * - SpelledOutAudioInput - The input type for the getSpelledOutAudio function.\n * - SpelledOutAudioOutput - The return type for the getSpelledOutAudio function.\n */\n\nimport {ai} from '@/ai/genkit';\nimport {googleAI} from '@genkit-ai/googleai';\nimport {z} from 'genkit';\nimport wav from 'wav';\n\nconst SpelledOutAudioInputSchema = z.object({\n  word: z.string().describe('The word to be spelled out.'),\n  lang: z.string().describe('The BCP-47 language code for the pronunciation voice.'),\n});\nexport type SpelledOutAudioInput = z.infer<typeof SpelledOutAudioInputSchema>;\n\nconst SpelledOutAudioOutputSchema = z.object({\n  audioDataUri: z.string().nullable().describe(\"A data URI of the WAV audio file. Expected format: 'data:audio/wav;base64,<encoded_data>'. Is null on failure.\"),\n});\nexport type SpelledOutAudioOutput = z.infer<typeof SpelledOutAudioOutputSchema>;\n\nexport async function getSpelledOutAudio(input: SpelledOutAudioInput): Promise<SpelledOutAudioOutput> {\n  return spellWordToAudioFlow(input);\n}\n\nasync function toWav(\n  pcmData: Buffer,\n  channels = 1,\n  rate = 24000,\n  sampleWidth = 2\n): Promise<string> {\n  return new Promise((resolve, reject) => {\n    const writer = new wav.Writer({\n      channels,\n      sampleRate: rate,\n      bitDepth: sampleWidth * 8,\n    });\n\n    const bufs: Buffer[] = [];\n    writer.on('error', reject);\n    writer.on('data', (d) => {\n      bufs.push(d);\n    });\n    writer.on('end', () => {\n      resolve(Buffer.concat(bufs).toString('base64'));\n    });\n\n    writer.write(pcmData);\n    writer.end();\n  });\n}\n\nconst spellWordToAudioFlow = ai.defineFlow(\n  {\n    name: 'spellWordToAudioFlow',\n    inputSchema: SpelledOutAudioInputSchema,\n    outputSchema: SpelledOutAudioOutputSchema,\n  },\n  async ({ word, lang }) => {\n    try {\n      // Format the word to be spelled out letter by letter.\n      const spelledOutWord = word.split('').join(' ');\n\n      const { media } = await ai.generate({\n        model: googleAI.model('gemini-2.5-flash-preview-tts'),\n        config: {\n          responseModalities: ['AUDIO'],\n          speechConfig: {\n            voiceConfig: {\n              prebuiltVoiceConfig: { voiceName: 'Algenib' }, // A standard voice\n            },\n          },\n        },\n        prompt: spelledOutWord,\n      });\n\n      if (!media) {\n        throw new Error('No audio media was generated by the model.');\n      }\n\n      const audioBuffer = Buffer.from(\n        media.url.substring(media.url.indexOf(',') + 1),\n        'base64'\n      );\n      \n      const wavBase64 = await toWav(audioBuffer);\n\n      return {\n        audioDataUri: 'data:audio/wav;base64,' + wavBase64,\n      };\n    } catch (error) {\n       const errorMessage = error instanceof Error ? error.message : String(error);\n       console.error(`[spellWordToAudioFlow] - Error: ${errorMessage}`, { word, lang });\n       // Return null instead of throwing an error to prevent server crashes.\n       return { audioDataUri: null };\n    }\n  }\n);\n"], "names": [], "mappings": ";;;;;AAEA;;;;;;CAMC,GAED;AACA;AAAA;AACA;AAAA;AACA;;;;;;;;AAEA,MAAM,6BAA6B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;IAC1B,MAAM,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,CAAC;AAC5B;AAGA,MAAM,8BAA8B,uIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,cAAc,uIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,CAAC;AAC/C;AAGO,eAAe,uCAAgB,GAAhB,mBAAmB,KAA2B;IAClE,OAAO,qBAAqB;AAC9B;AAEA,eAAe,MACb,OAAe,EACf,WAAW,CAAC,EACZ,OAAO,KAAK,EACZ,cAAc,CAAC;IAEf,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,MAAM,SAAS,IAAI,4HAAA,CAAA,UAAG,CAAC,MAAM,CAAC;YAC5B;YACA,YAAY;YACZ,UAAU,cAAc;QAC1B;QAEA,MAAM,OAAiB,EAAE;QACzB,OAAO,EAAE,CAAC,SAAS;QACnB,OAAO,EAAE,CAAC,QAAQ,CAAC;YACjB,KAAK,IAAI,CAAC;QACZ;QACA,OAAO,EAAE,CAAC,OAAO;YACf,QAAQ,OAAO,MAAM,CAAC,MAAM,QAAQ,CAAC;QACvC;QAEA,OAAO,KAAK,CAAC;QACb,OAAO,GAAG;IACZ;AACF;AAEA,MAAM,uBAAuB,mHAAA,CAAA,KAAE,CAAC,UAAU,CACxC;IACE,MAAM;IACN,aAAa;IACb,cAAc;AAChB,GACA,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE;IACnB,IAAI;QACF,sDAAsD;QACtD,MAAM,iBAAiB,KAAK,KAAK,CAAC,IAAI,IAAI,CAAC;QAE3C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,mHAAA,CAAA,KAAE,CAAC,QAAQ,CAAC;YAClC,OAAO,2KAAA,CAAA,WAAQ,CAAC,KAAK,CAAC;YACtB,QAAQ;gBACN,oBAAoB;oBAAC;iBAAQ;gBAC7B,cAAc;oBACZ,aAAa;wBACX,qBAAqB;4BAAE,WAAW;wBAAU;oBAC9C;gBACF;YACF;YACA,QAAQ;QACV;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,cAAc,OAAO,IAAI,CAC7B,MAAM,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,IAC7C;QAGF,MAAM,YAAY,MAAM,MAAM;QAE9B,OAAO;YACL,cAAc,2BAA2B;QAC3C;IACF,EAAE,OAAO,OAAO;QACb,MAAM,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACrE,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,cAAc,EAAE;YAAE;YAAM;QAAK;QAC9E,sEAAsE;QACtE,OAAO;YAAE,cAAc;QAAK;IAC/B;AACF;;;IA3EoB;;AAAA,+OAAA", "debugId": null}}, {"offset": {"line": 1315, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1411, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA", "debugId": null}}, {"offset": {"line": 1425, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/VsCode%20Projects/LinguaFlow/src/app/page.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgQ,GAC7R,8BACA", "debugId": null}}, {"offset": {"line": 1439, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}]}