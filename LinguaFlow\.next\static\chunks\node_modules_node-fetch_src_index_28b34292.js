(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_node-fetch_src_index_027dae29.js", {

"[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_node-fetch_src_utils_multipart-parser_a98db7d7.js",
  "static/chunks/node_modules_0d629be2._.js",
  "static/chunks/node_modules_node-fetch_src_index_ef00ab5a.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);