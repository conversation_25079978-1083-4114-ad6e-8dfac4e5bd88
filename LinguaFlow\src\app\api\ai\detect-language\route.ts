import { NextRequest, NextResponse } from 'next/server';
import { detectLanguage } from '@/ai/flows/language-detection-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await detectLanguage({ text });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in detect-language API:', error);
    return NextResponse.json(
      { error: 'Failed to detect language' },
      { status: 500 }
    );
  }
}
