(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push(["static/chunks/node_modules_node-fetch_src_index_7cd74060.js", {

"[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_node-fetch_src_utils_multipart-parser_85218b27.js",
  "static/chunks/node_modules_0d629be2._.js",
  "static/chunks/node_modules_node-fetch_src_index_7201dc84.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/node-fetch/src/index.js [app-client] (ecmascript)");
    });
});
}}),
}]);