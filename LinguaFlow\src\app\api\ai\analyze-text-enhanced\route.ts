import { NextRequest, NextResponse } from 'next/server';
import { analyzeTextEnhanced } from '@/ai/flows/enhanced-text-analysis-flow';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { text, language, analysisType } = body;

    if (!text || typeof text !== 'string') {
      return NextResponse.json(
        { error: 'Text is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await analyzeTextEnhanced({ 
      text,
      language: language || 'en',
      analysisType: analysisType || 'comprehensive'
    });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in analyze-text-enhanced API:', error);
    return NextResponse.json(
      { error: 'Failed to analyze text (enhanced)' },
      { status: 500 }
    );
  }
}
