import { NextRequest, NextResponse } from 'next/server';
import { rephraseText } from '@/ai/flows/contextual-ai-rephraser';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { selectedText, contextText, tone, style } = body;

    if (!selectedText || typeof selectedText !== 'string') {
      return NextResponse.json(
        { error: 'selectedText is required and must be a string' },
        { status: 400 }
      );
    }

    const result = await rephraseText({
      selectedText,
      contextText: contextText || selectedText,
      tone: tone || 'general',
      style: style || 'general'
    });
    return NextResponse.json(result);
  } catch (error) {
    console.error('Error in rephrase-text API:', error);
    return NextResponse.json(
      { error: 'Failed to rephrase text' },
      { status: 500 }
    );
  }
}
